<?php
/**
 * ESP32 Device Management Interface
 * Manage ESP32 devices, thresholds, and configurations
 */

require_once 'api/config.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pdo = getDBConnection();
    
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_thresholds':
                updateThresholds($pdo, $_POST);
                break;
            case 'resolve_alert':
                resolveAlert($pdo, $_POST['alert_id']);
                break;
            case 'update_device':
                updateDevice($pdo, $_POST);
                break;
        }
    }
}

function updateThresholds($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            UPDATE sensor_thresholds 
            SET min_value = ?, max_value = ?, alert_enabled = ?
            WHERE id = ?
        ");
        
        foreach ($data['threshold_id'] as $index => $id) {
            $stmt->execute([
                $data['min_value'][$index],
                $data['max_value'][$index],
                isset($data['alert_enabled'][$index]) ? 1 : 0,
                $id
            ]);
        }
        
        $success_message = "Thresholds updated successfully!";
    } catch (Exception $e) {
        $error_message = "Error updating thresholds: " . $e->getMessage();
    }
}

function resolveAlert($pdo, $alert_id) {
    try {
        $stmt = $pdo->prepare("
            UPDATE device_alerts 
            SET resolved = 1, resolved_at = NOW(), resolved_by = 'Admin'
            WHERE id = ?
        ");
        $stmt->execute([$alert_id]);
        
        $success_message = "Alert resolved successfully!";
    } catch (Exception $e) {
        $error_message = "Error resolving alert: " . $e->getMessage();
    }
}

function updateDevice($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            UPDATE esp32_devices 
            SET device_name = ?, location = ?
            WHERE device_id = ?
        ");
        $stmt->execute([
            $data['device_name'],
            $data['location'],
            $data['device_id']
        ]);
        
        $success_message = "Device updated successfully!";
    } catch (Exception $e) {
        $error_message = "Error updating device: " . $e->getMessage();
    }
}

// Get database connection
$pdo = getDBConnection();

// Get all devices
$devices_stmt = $pdo->query("SELECT * FROM esp32_devices ORDER BY device_id");
$devices = $devices_stmt->fetchAll();

// Get sensor thresholds
$thresholds_stmt = $pdo->query("
    SELECT * FROM sensor_thresholds 
    ORDER BY location, sensor_type
");
$thresholds = $thresholds_stmt->fetchAll();

// Get active alerts
$alerts_stmt = $pdo->query("
    SELECT da.*, ed.device_name 
    FROM device_alerts da
    LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
    WHERE da.resolved = 0
    ORDER BY da.severity DESC, da.timestamp DESC
");
$alerts = $alerts_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Device Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .nav a {
            color: #667eea;
            text-decoration: none;
            margin-right: 2rem;
            font-weight: 500;
        }

        .nav a:hover {
            text-decoration: underline;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            color: #495057;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: #667eea;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background-color: #d4edda;
            color: #155724;
        }

        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 ESP32 Device Management</h1>
    </div>

    <div class="nav">
        <a href="esp32_dashboard.php">📊 Dashboard</a>
        <a href="device_management.php">🔧 Device Management</a>
        <a href="livestock_tracker.sql">📁 Database</a>
    </div>

    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>

        <div class="grid">
            <!-- Device Management -->
            <div class="card">
                <div class="card-header">📡 Device Management</div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Device ID</th>
                                <th>Name</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Last Seen</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($devices as $device): ?>
                            <tr>
                                <td><?= htmlspecialchars($device['device_id']) ?></td>
                                <td><?= htmlspecialchars($device['device_name']) ?></td>
                                <td><?= htmlspecialchars($device['location']) ?></td>
                                <td>
                                    <span class="status-badge status-<?= strtolower($device['status']) ?>">
                                        <?= htmlspecialchars($device['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($device['last_seen']): ?>
                                        <?= date('M j, H:i', strtotime($device['last_seen'])) ?>
                                    <?php else: ?>
                                        Never
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-primary" onclick="editDevice('<?= $device['device_id'] ?>', '<?= htmlspecialchars($device['device_name']) ?>', '<?= htmlspecialchars($device['location']) ?>')">
                                        Edit
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Active Alerts Management -->
            <div class="card">
                <div class="card-header">🚨 Active Alerts</div>
                <div class="card-body">
                    <?php if (empty($alerts)): ?>
                        <p>No active alerts.</p>
                    <?php else: ?>
                        <?php foreach ($alerts as $alert): ?>
                            <div style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 5px;">
                                <strong><?= htmlspecialchars($alert['alert_type']) ?></strong>
                                <span style="float: right; color: #666;"><?= htmlspecialchars($alert['severity']) ?></span>
                                <br>
                                <small><?= htmlspecialchars($alert['alert_message']) ?></small>
                                <br>
                                <small style="color: #666;">
                                    Device: <?= htmlspecialchars($alert['device_name']) ?> • 
                                    <?= date('M j, H:i', strtotime($alert['timestamp'])) ?>
                                </small>
                                <br>
                                <form method="POST" style="margin-top: 0.5rem;">
                                    <input type="hidden" name="action" value="resolve_alert">
                                    <input type="hidden" name="alert_id" value="<?= $alert['id'] ?>">
                                    <button type="submit" class="btn btn-success">Resolve</button>
                                </form>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sensor Thresholds -->
        <div class="card">
            <div class="card-header">⚙️ Sensor Thresholds</div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_thresholds">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Location</th>
                                <th>Sensor Type</th>
                                <th>Min Value</th>
                                <th>Max Value</th>
                                <th>Alerts Enabled</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($thresholds as $threshold): ?>
                            <tr>
                                <td><?= htmlspecialchars($threshold['location'] ?: 'Default') ?></td>
                                <td><?= htmlspecialchars($threshold['sensor_type']) ?></td>
                                <td>
                                    <input type="hidden" name="threshold_id[]" value="<?= $threshold['id'] ?>">
                                    <input type="number" step="0.1" name="min_value[]" value="<?= $threshold['min_value'] ?>" class="form-control" style="width: 100px;">
                                </td>
                                <td>
                                    <input type="number" step="0.1" name="max_value[]" value="<?= $threshold['max_value'] ?>" class="form-control" style="width: 100px;">
                                </td>
                                <td>
                                    <input type="checkbox" name="alert_enabled[]" value="1" <?= $threshold['alert_enabled'] ? 'checked' : '' ?>>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <button type="submit" class="btn btn-primary">Update Thresholds</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Device Edit Modal (Simple JavaScript) -->
    <div id="deviceModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 10px; width: 400px;">
            <h3>Edit Device</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_device">
                <input type="hidden" name="device_id" id="edit_device_id">
                
                <div class="form-group">
                    <label>Device Name:</label>
                    <input type="text" name="device_name" id="edit_device_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label>Location:</label>
                    <input type="text" name="location" id="edit_location" class="form-control" required>
                </div>
                
                <button type="submit" class="btn btn-primary">Update Device</button>
                <button type="button" class="btn btn-danger" onclick="closeModal()">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        function editDevice(deviceId, deviceName, location) {
            document.getElementById('edit_device_id').value = deviceId;
            document.getElementById('edit_device_name').value = deviceName;
            document.getElementById('edit_location').value = location;
            document.getElementById('deviceModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('deviceModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('deviceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
