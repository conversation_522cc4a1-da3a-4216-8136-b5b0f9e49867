<?php
/**
 * Health Records Management - Add, Edit, Delete Health Records
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_health_record':
            addHealthRecord($pdo, $_POST);
            break;
        case 'edit_health_record':
            editHealthRecord($pdo, $_POST);
            break;
        case 'delete_health_record':
            deleteHealthRecord($pdo, $_POST['record_id']);
            break;
    }
    
    // Redirect to prevent form resubmission
    header("Location: manage_health.php");
    exit;
}

// Get health record for editing if ID provided
$edit_record = null;
if (isset($_GET['edit'])) {
    $stmt = $pdo->prepare("
        SELECT hr.*, a.tag_number, a.species 
        FROM health_records hr
        JOIN animals a ON hr.animal_id = a.animal_id
        WHERE hr.record_id = ?
    ");
    $stmt->execute([$_GET['edit']]);
    $edit_record = $stmt->fetch();
}

// Get all health records with animal details
$health_records_stmt = $pdo->query("
    SELECT 
        hr.*,
        a.tag_number,
        a.species,
        a.breed
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    WHERE a.status = 'Active'
    ORDER BY hr.record_date DESC, hr.record_id DESC
    LIMIT 100
");
$health_records = $health_records_stmt->fetchAll();

// Get active animals for dropdown
$animals_stmt = $pdo->query("
    SELECT animal_id, tag_number, species, breed 
    FROM animals 
    WHERE status = 'Active' 
    ORDER BY tag_number
");
$animals = $animals_stmt->fetchAll();

function addHealthRecord($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO health_records (animal_id, record_date, temperature, health_status, treatment, notes, veterinarian)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $data['animal_id'],
            $data['record_date'],
            $data['temperature'] ?: null,
            $data['health_status'],
            $data['treatment'] ?? '',
            $data['notes'] ?? '',
            $data['veterinarian'] ?? ''
        ]);
        $_SESSION['success'] = "Health record added successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error adding health record: " . $e->getMessage();
    }
}

function editHealthRecord($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            UPDATE health_records
            SET animal_id = ?, record_date = ?, temperature = ?, health_status = ?, treatment = ?, notes = ?, veterinarian = ?
            WHERE record_id = ?
        ");
        $stmt->execute([
            $data['animal_id'],
            $data['record_date'],
            $data['temperature'] ?: null,
            $data['health_status'],
            $data['treatment'] ?? '',
            $data['notes'] ?? '',
            $data['veterinarian'] ?? '',
            $data['record_id']
        ]);
        $_SESSION['success'] = "Health record updated successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error updating health record: " . $e->getMessage();
    }
}

function deleteHealthRecord($pdo, $record_id) {
    try {
        $stmt = $pdo->prepare("DELETE FROM health_records WHERE record_id = ?");
        $stmt->execute([$record_id]);
        $_SESSION['success'] = "Health record deleted successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error deleting health record: " . $e->getMessage();
    }
}

session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Health Records - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .btn-success {
            background-color: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background-color: #38a169;
        }

        .btn-danger {
            background-color: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background-color: #e53e3e;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Forms */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4a6cf7;
            box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-healthy {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-sick {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-monitoring {
            background-color: #fbb6ce;
            color: #97266d;
        }

        .status-critical {
            background-color: #feb2b2;
            color: #742a2a;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .close-btn:hover {
            color: #374151;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link active">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> AI Insights
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">Manage Health Records</h1>
        <div>
            <a href="health.php" class="btn" style="background: #6b7280; color: white; margin-right: 1rem;">
                <i class="fas fa-arrow-left"></i> Back to Health
            </a>
            <button class="btn btn-primary" onclick="openAddModal()">
                <i class="fas fa-plus"></i> Add Health Record
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Health Records Table -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Health Records</h3>
            </div>
            <div class="card-body">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Animal</th>
                            <th>Species</th>
                            <th>Temperature</th>
                            <th>Health Status</th>
                            <th>Treatment</th>
                            <th>Veterinarian</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($health_records as $record): ?>
                        <tr>
                            <td><?= date('M j, Y', strtotime($record['record_date'])) ?></td>
                            <td><strong><?= htmlspecialchars($record['tag_number']) ?></strong></td>
                            <td><?= htmlspecialchars($record['species']) ?></td>
                            <td><?= $record['temperature'] ? number_format($record['temperature'], 1) . '°C' : '--' ?></td>
                            <td>
                                <span class="status-badge status-<?= strtolower($record['health_status']) ?>">
                                    <?= htmlspecialchars($record['health_status']) ?>
                                </span>
                            </td>
                            <td><?= ($record['treatment'] ?? '') ? htmlspecialchars(substr($record['treatment'], 0, 30)) . (strlen($record['treatment']) > 30 ? '...' : '') : '--' ?></td>
                            <td><?= htmlspecialchars($record['veterinarian'] ?? '--') ?></td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editRecord(<?= htmlspecialchars(json_encode($record)) ?>)">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="confirmDelete(<?= $record['record_id'] ?>, '<?= htmlspecialchars($record['tag_number']) ?>')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add/Edit Health Record Modal -->
    <div id="healthModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Add Health Record</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="healthForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add_health_record">
                <input type="hidden" name="record_id" id="recordId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Animal *</label>
                        <select name="animal_id" id="animalId" class="form-control" required>
                            <option value="">Select Animal</option>
                            <?php foreach ($animals as $animal): ?>
                                <option value="<?= $animal['animal_id'] ?>">
                                    <?= htmlspecialchars($animal['tag_number']) ?> - <?= htmlspecialchars($animal['species']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Record Date *</label>
                        <input type="date" name="record_date" id="recordDate" class="form-control" required value="<?= date('Y-m-d') ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Health Status *</label>
                        <select name="health_status" id="healthStatus" class="form-control" required>
                            <option value="">Select Status</option>
                            <option value="Healthy">Healthy</option>
                            <option value="Monitoring">Monitoring</option>
                            <option value="Sick">Sick</option>
                            <option value="Critical">Critical</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Temperature (°C)</label>
                        <input type="number" step="0.1" name="temperature" id="temperature" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Veterinarian</label>
                        <input type="text" name="veterinarian" id="veterinarian" class="form-control">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Treatment</label>
                        <textarea name="treatment" id="treatment" class="form-control" rows="2"></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #6b7280; color: white;">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
            </div>
            <p>Are you sure you want to delete this health record for <strong id="deleteAnimalName"></strong>?</p>
            <p style="color: #6b7280; font-size: 0.875rem;">This action cannot be undone.</p>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                <button type="button" class="btn" onclick="closeDeleteModal()" style="background: #6b7280; color: white;">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_health_record">
                    <input type="hidden" name="record_id" id="deleteRecordId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'Add Health Record';
            document.getElementById('formAction').value = 'add_health_record';
            document.getElementById('healthForm').reset();
            document.getElementById('recordId').value = '';
            document.getElementById('recordDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('healthModal').classList.add('show');
        }

        function editRecord(record) {
            document.getElementById('modalTitle').textContent = 'Edit Health Record';
            document.getElementById('formAction').value = 'edit_health_record';
            document.getElementById('recordId').value = record.record_id;
            document.getElementById('animalId').value = record.animal_id;
            document.getElementById('recordDate').value = record.record_date;
            document.getElementById('healthStatus').value = record.health_status;

            document.getElementById('temperature').value = record.temperature || '';
            document.getElementById('veterinarian').value = record.veterinarian || '';
            document.getElementById('treatment').value = record.treatment || '';
            document.getElementById('notes').value = record.notes || '';
            document.getElementById('healthModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('healthModal').classList.remove('show');
        }

        function confirmDelete(recordId, animalName) {
            document.getElementById('deleteRecordId').value = recordId;
            document.getElementById('deleteAnimalName').textContent = animalName;
            document.getElementById('deleteModal').classList.add('show');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const healthModal = document.getElementById('healthModal');
            const deleteModal = document.getElementById('deleteModal');
            
            if (event.target === healthModal) {
                closeModal();
            }
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
        }
    </script>
</body>
</html>
