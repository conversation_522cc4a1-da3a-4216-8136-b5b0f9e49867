-- Database Setup Script for ESP32 Livestock Management System
-- Run this script to set up the database for ESP32 integration

-- Use the livestock_tracker database
USE livestock_tracker;

-- Source the ESP32 database updates
SOURCE esp32_database_updates.sql;

-- Create additional indexes for better performance
CREATE INDEX idx_sensor_data_device_timestamp ON sensor_data(device_id, timestamp);
CREATE INDEX idx_realtime_sensor_location ON realtime_sensor_data(location, timestamp);
CREATE INDEX idx_device_alerts_severity ON device_alerts(severity, resolved);

-- Create user for ESP32 API (optional - for better security)
-- Uncomment and modify if you want a dedicated database user
/*
CREATE USER 'esp32_user'@'%' IDENTIFIED BY 'secure_password_here';
GRANT SELECT, INSERT, UPDATE ON livestock_tracker.* TO 'esp32_user'@'%';
GRANT EXECUTE ON livestock_tracker.* TO 'esp32_user'@'%';
FLUSH PRIVILEGES;
*/

-- Insert sample configuration data
INSERT INTO sensor_thresholds (location, sensor_type, min_value, max_value, alert_enabled) VALUES
('Default', 'temperature', 10.0, 30.0, 1),
('Default', 'humidity', 30.0, 80.0, 1),
('Default', 'activity', 0.5, 10.0, 1)
ON DUPLICATE KEY UPDATE
min_value = VALUES(min_value),
max_value = VALUES(max_value);

-- Create a test device entry
INSERT INTO esp32_devices (device_id, device_name, location, status) VALUES
('ESP32_TEST', 'Test Device', 'Test Location', 'Offline')
ON DUPLICATE KEY UPDATE
device_name = VALUES(device_name),
location = VALUES(location);

-- Verify the setup
SELECT 'Database setup completed successfully' as status;
SELECT COUNT(*) as esp32_devices_count FROM esp32_devices;
SELECT COUNT(*) as sensor_thresholds_count FROM sensor_thresholds;

-- Show table structure for verification
DESCRIBE esp32_devices;
DESCRIBE realtime_sensor_data;
DESCRIBE device_alerts;
DESCRIBE sensor_thresholds;
