<?php
/**
 * Test SQL Fixes
 * Test all SQL queries to ensure no ambiguity errors
 */

require_once 'api/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>SQL Test Results</title>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    h1 { color: #333; }
    h2 { color: #666; margin-top: 30px; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>";
echo "</head><body>";

echo "<h1>🧪 SQL Query Test Results</h1>";

try {
    $pdo = getDBConnection();
    echo "<div class='test-result success'>✅ Database connection successful!</div>";
} catch (Exception $e) {
    echo "<div class='test-result error'>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "</body></html>";
    exit;
}

// Test queries from each page
$tests = [
    'Dashboard - Device Stats' => "
        SELECT 
            (SELECT COUNT(*) FROM esp32_devices) as total_devices,
            (SELECT COUNT(*) FROM esp32_devices WHERE status = 'Online') as online_devices,
            (SELECT COUNT(*) FROM device_alerts WHERE resolved = 0) as active_alerts,
            (SELECT COUNT(*) FROM realtime_sensor_data WHERE timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)) as recent_readings
    ",
    
    'Animals - Species Distribution' => "
        SELECT species, COUNT(*) as count 
        FROM animals 
        WHERE status = 'Active' 
        GROUP BY species
        LIMIT 5
    ",
    
    'Health - Health Stats' => "
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN record_date > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_records,
            COUNT(CASE WHEN health_status = 'Healthy' THEN 1 END) as healthy_count,
            AVG(weight) as avg_weight
        FROM health_records hr
        JOIN animals a ON hr.animal_id = a.animal_id
        WHERE a.status = 'Active'
        AND hr.record_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
    ",
    
    'IoT Data - Device Status' => "
        SELECT 
            ed.*,
            lsr.temperature,
            lsr.humidity,
            lsr.activity_level,
            lsr.motion_detected,
            lsr.timestamp as last_reading
        FROM esp32_devices ed
        LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
        ORDER BY ed.device_id
        LIMIT 5
    ",
    
    'IoT Data - Statistics (Fixed)' => "
        SELECT 
            COUNT(DISTINCT ed.device_id) as total_devices,
            COUNT(CASE WHEN ed.status = 'Online' THEN 1 END) as online_devices,
            COUNT(rsd.id) as total_readings,
            AVG(CASE WHEN rsd.temperature IS NOT NULL THEN rsd.temperature END) as avg_temperature,
            AVG(CASE WHEN rsd.humidity IS NOT NULL THEN rsd.humidity END) as avg_humidity
        FROM esp32_devices ed
        LEFT JOIN realtime_sensor_data rsd ON ed.device_id = rsd.device_id 
        WHERE rsd.timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR) OR rsd.timestamp IS NULL
    ",
    
    'Alerts - Active Alerts' => "
        SELECT 
            da.*,
            ed.device_name,
            ed.location as device_location
        FROM device_alerts da
        LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
        WHERE da.resolved = 0
        ORDER BY da.timestamp DESC
        LIMIT 5
    ",
    
    'Alerts - Device Alert Count (Fixed)' => "
        SELECT 
            da.device_id,
            COUNT(*) as alert_count
        FROM device_alerts da
        WHERE da.timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND da.resolved = 0
        GROUP BY da.device_id
    "
];

foreach ($tests as $test_name => $query) {
    echo "<h2>🔍 Testing: $test_name</h2>";
    
    try {
        $stmt = $pdo->query($query);
        $results = $stmt->fetchAll();
        
        echo "<div class='test-result success'>";
        echo "✅ Query executed successfully!<br>";
        echo "📊 Returned " . count($results) . " rows<br>";
        
        if (count($results) > 0) {
            echo "<strong>Sample data:</strong><br>";
            echo "<pre>" . htmlspecialchars(print_r($results[0], true)) . "</pre>";
        } else {
            echo "<em>No data returned (this might be normal if tables are empty)</em>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-result error'>";
        echo "❌ Query failed: " . htmlspecialchars($e->getMessage()) . "<br>";
        echo "<strong>Query:</strong><br>";
        echo "<pre>" . htmlspecialchars($query) . "</pre>";
        echo "</div>";
    }
}

// Test table existence
echo "<h2>📋 Table Existence Check</h2>";
$required_tables = [
    'animals',
    'health_records', 
    'esp32_devices',
    'realtime_sensor_data',
    'device_alerts',
    'sensor_thresholds'
];

foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch();
        echo "<div class='test-result success'>✅ Table '$table' exists with {$result['count']} records</div>";
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ Table '$table' missing or inaccessible: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

// Test views
echo "<h2>👁️ View Existence Check</h2>";
$views = ['latest_sensor_readings', 'active_device_alerts'];

foreach ($views as $view) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $view");
        $result = $stmt->fetch();
        echo "<div class='test-result success'>✅ View '$view' exists with {$result['count']} records</div>";
    } catch (Exception $e) {
        echo "<div class='test-result warning'>⚠️ View '$view' missing: " . htmlspecialchars($e->getMessage()) . "</div>";
        echo "<div class='test-result warning'>💡 This view should be created by running the database setup scripts</div>";
    }
}

echo "<h2>🔗 Navigation Test Links</h2>";
echo "<p>If all SQL tests passed, try these navigation links:</p>";

$pages = [
    'esp32_dashboard.php' => 'Dashboard',
    'animals.php' => 'Animals',
    'health.php' => 'Health', 
    'iot_data.php' => 'IoT Data',
    'alerts.php' => 'Alerts',
    'ai_insights.php' => 'AI Insights'
];

foreach ($pages as $file => $name) {
    if (file_exists($file)) {
        echo "<a href='$file' style='display:inline-block;margin:5px;padding:10px;background:#4a6cf7;color:white;text-decoration:none;border-radius:5px;'>$name</a> ";
    } else {
        echo "<span style='display:inline-block;margin:5px;padding:10px;background:#dc3545;color:white;border-radius:5px;'>$name (Missing)</span> ";
    }
}

echo "<div style='margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;'>";
echo "<h3>📝 Summary</h3>";
echo "<p>If you see any red error messages above, those need to be fixed before the navigation will work properly.</p>";
echo "<p>Common issues:</p>";
echo "<ul>";
echo "<li>Missing database tables (run the database setup scripts)</li>";
echo "<li>Missing views (run esp32_database_updates.sql)</li>";
echo "<li>Incorrect database credentials in api/config.php</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
