-- ESP32 Livestock Management Database Updates
-- Add tables and columns to support ESP32 sensor integration

-- Create table for ESP32 devices
CREATE TABLE IF NOT EXISTS `esp32_devices` (
  `device_id` varchar(50) NOT NULL,
  `device_name` varchar(100) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `status` enum('Online','Offline','Error') DEFAULT 'Offline',
  `firmware_version` varchar(20) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample ESP32 device
INSERT INTO `esp32_devices` (`device_id`, `device_name`, `location`, `status`) VALUES
('ESP32_001', 'Barn A Monitor', 'Barn A', 'Offline'),
('ESP32_002', 'Pasture 1 Monitor', 'Pasture 1', 'Offline'),
('ESP32_003', 'Barn B Monitor', 'Barn B', 'Offline');

-- Update existing sensor_data table to include device_id and motion detection
ALTER TABLE `sensor_data` 
ADD COLUMN `device_id` varchar(50) DEFAULT NULL AFTER `id`,
ADD COLUMN `motion_detected` tinyint(1) DEFAULT 0 AFTER `ir_status`,
ADD INDEX `idx_device_id` (`device_id`),
ADD INDEX `idx_timestamp` (`timestamp`);

-- Create table for real-time sensor readings (for live monitoring)
CREATE TABLE IF NOT EXISTS `realtime_sensor_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `temperature` float DEFAULT NULL,
  `humidity` float DEFAULT NULL,
  `activity_level` float DEFAULT NULL,
  `motion_detected` tinyint(1) DEFAULT 0,
  `location` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_device_timestamp` (`device_id`, `timestamp`),
  KEY `idx_location` (`location`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create table for device alerts and notifications
CREATE TABLE IF NOT EXISTS `device_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `alert_type` varchar(50) NOT NULL,
  `alert_message` text NOT NULL,
  `severity` enum('Low','Medium','High','Critical') DEFAULT 'Medium',
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `resolved` tinyint(1) DEFAULT 0,
  `resolved_at` datetime DEFAULT NULL,
  `resolved_by` varchar(100) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_device_alerts` (`device_id`, `resolved`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_severity` (`severity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create table for sensor thresholds and configurations
CREATE TABLE IF NOT EXISTS `sensor_thresholds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL,
  `sensor_type` varchar(50) NOT NULL,
  `min_value` float DEFAULT NULL,
  `max_value` float DEFAULT NULL,
  `alert_enabled` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_sensor` (`device_id`, `sensor_type`),
  KEY `idx_location_sensor` (`location`, `sensor_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default sensor thresholds
INSERT INTO `sensor_thresholds` (`device_id`, `location`, `sensor_type`, `min_value`, `max_value`, `alert_enabled`) VALUES
(NULL, 'Barn A', 'temperature', 10.0, 30.0, 1),
(NULL, 'Barn A', 'humidity', 30.0, 80.0, 1),
(NULL, 'Barn A', 'activity', 0.5, 10.0, 1),
(NULL, 'Pasture 1', 'temperature', 5.0, 35.0, 1),
(NULL, 'Pasture 1', 'humidity', 25.0, 85.0, 1),
(NULL, 'Pasture 1', 'activity', 0.3, 15.0, 1),
(NULL, 'Barn B', 'temperature', 10.0, 30.0, 1),
(NULL, 'Barn B', 'humidity', 30.0, 80.0, 1),
(NULL, 'Barn B', 'activity', 0.5, 10.0, 1);

-- Create table for device maintenance and logs
CREATE TABLE IF NOT EXISTS `device_maintenance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `maintenance_type` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `performed_by` varchar(100) DEFAULT NULL,
  `performed_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `next_maintenance` date DEFAULT NULL,
  `status` enum('Scheduled','Completed','Overdue') DEFAULT 'Completed',
  PRIMARY KEY (`id`),
  KEY `idx_device_maintenance` (`device_id`, `status`),
  KEY `idx_next_maintenance` (`next_maintenance`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create view for latest sensor readings per device
CREATE OR REPLACE VIEW `latest_sensor_readings` AS
SELECT 
  rsd.device_id,
  ed.device_name,
  ed.location,
  rsd.temperature,
  rsd.humidity,
  rsd.activity_level,
  rsd.motion_detected,
  rsd.timestamp,
  ed.status as device_status
FROM `realtime_sensor_data` rsd
INNER JOIN `esp32_devices` ed ON rsd.device_id = ed.device_id
INNER JOIN (
  SELECT device_id, MAX(timestamp) as max_timestamp
  FROM `realtime_sensor_data`
  GROUP BY device_id
) latest ON rsd.device_id = latest.device_id AND rsd.timestamp = latest.max_timestamp;

-- Create view for active alerts
CREATE OR REPLACE VIEW `active_device_alerts` AS
SELECT 
  da.id,
  da.device_id,
  ed.device_name,
  da.alert_type,
  da.alert_message,
  da.severity,
  da.timestamp,
  da.location
FROM `device_alerts` da
INNER JOIN `esp32_devices` ed ON da.device_id = ed.device_id
WHERE da.resolved = 0
ORDER BY da.severity DESC, da.timestamp DESC;

-- Create stored procedure to clean old sensor data
DELIMITER //
CREATE PROCEDURE CleanOldSensorData(IN days_to_keep INT)
BEGIN
  -- Delete sensor data older than specified days
  DELETE FROM `realtime_sensor_data` 
  WHERE `timestamp` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
  
  -- Delete resolved alerts older than specified days
  DELETE FROM `device_alerts` 
  WHERE `resolved` = 1 AND `resolved_at` < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
  
  SELECT ROW_COUNT() as deleted_records;
END //
DELIMITER ;

-- Create stored procedure to update device status
DELIMITER //
CREATE PROCEDURE UpdateDeviceStatus(IN device_id_param VARCHAR(50), IN ip_addr VARCHAR(45))
BEGIN
  UPDATE `esp32_devices` 
  SET 
    `last_seen` = NOW(),
    `status` = 'Online',
    `ip_address` = ip_addr
  WHERE `device_id` = device_id_param;
  
  -- If device doesn't exist, create it
  INSERT IGNORE INTO `esp32_devices` (`device_id`, `device_name`, `status`, `ip_address`, `last_seen`)
  VALUES (device_id_param, CONCAT('Device ', device_id_param), 'Online', ip_addr, NOW());
END //
DELIMITER ;

-- Create event to automatically mark devices offline if no data received
SET GLOBAL event_scheduler = ON;

DELIMITER //
CREATE EVENT IF NOT EXISTS MarkOfflineDevices
ON SCHEDULE EVERY 5 MINUTE
DO
BEGIN
  UPDATE `esp32_devices` 
  SET `status` = 'Offline' 
  WHERE `last_seen` < DATE_SUB(NOW(), INTERVAL 10 MINUTE) 
  AND `status` = 'Online';
END //
DELIMITER ;
