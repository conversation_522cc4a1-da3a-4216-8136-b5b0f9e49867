# ESP32 Livestock Management System Setup Instructions

## Overview
This system integrates ESP32 microcontrollers with DHT11, MPU9250, and buzzer sensors to monitor livestock conditions and send data to your existing MySQL database.

## Prerequisites
- XAMPP server running (Apache + MySQL)
- Arduino IDE installed
- ESP32 development board
- Required sensors (<PERSON><PERSON>11, <PERSON>U9250, <PERSON><PERSON>)
- WiFi network access

## Step 1: Database Setup

### 1.1 Import Database Updates
```sql
-- Open phpMyAdmin or MySQL command line
-- Navigate to your livestock_tracker database
-- Run the following files in order:

1. esp32_database_updates.sql
2. config/database_setup.sql
```

### 1.2 Verify Database Setup
```sql
-- Check if new tables were created
SHOW TABLES LIKE '%esp32%';
SHOW TABLES LIKE '%realtime%';
SHOW TABLES LIKE '%device%';

-- Verify sample data
SELECT * FROM esp32_devices;
SELECT * FROM sensor_thresholds;
```

## Step 2: PHP API Setup

### 2.1 Copy API Files
Copy the following files to your XAMPP htdocs/livestock/ directory:
```
livestock/
├── api/
│   ├── config.php
│   ├── sensor_data.php
│   ├── alerts.php
│   └── devices.php
```

### 2.2 Configure Database Connection
Edit `api/config.php` and update:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', ''); // Your MySQL password
define('DB_NAME', 'livestock_tracker');
```

### 2.3 Test API Endpoints
Test the APIs using a web browser or Postman:
```
GET http://localhost/livestock/api/devices.php
GET http://localhost/livestock/api/sensor_data.php
GET http://localhost/livestock/api/alerts.php
```

## Step 3: Arduino IDE Setup

### 3.1 Install Required Libraries
Follow the instructions in `config/arduino_libraries.txt`:

1. Install ESP32 board package
2. Install required libraries:
   - ArduinoJson
   - DHT sensor library
   - Adafruit Unified Sensor
   - MPU9250
   - Wire (built-in)

### 3.2 Hardware Connections
```
ESP32 Connections:
├── DHT11
│   ├── Data Pin → D4 (GPIO 4)
│   ├── VCC → 3.3V
│   └── GND → GND
├── MPU9250
│   ├── SDA → D2 (GPIO 2)
│   ├── SCL → D18 (GPIO 18)
│   ├── VCC → 3.3V
│   └── GND → GND
└── Buzzer
    ├── Positive → D5 (GPIO 5)
    └── Negative → GND
```

## Step 4: ESP32 Configuration

### 4.1 Update Configuration
Edit `config/esp32_config.h`:
```cpp
// WiFi Settings
#define WIFI_SSID "Your_WiFi_Name"
#define WIFI_PASSWORD "Your_WiFi_Password"

// Server Settings
#define SERVER_HOST "*************"  // Your computer's IP

// Device Settings
#define DEVICE_ID "ESP32_001"        // Unique for each device
#define DEVICE_LOCATION "Barn A"     // Physical location
```

### 4.2 Find Your Computer's IP Address
**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" under your active network adapter.

**Linux/Mac:**
```bash
ifconfig
```
Look for "inet" address under your active network interface.

### 4.3 Upload Code to ESP32
1. Open `livestock_esp32.ino` in Arduino IDE
2. Include the configuration: `#include "config/esp32_config.h"`
3. Select Board: "ESP32 Dev Module"
4. Select correct COM port
5. Upload the code

## Step 5: Testing the System

### 5.1 Monitor Serial Output
Open Serial Monitor (115200 baud) to see:
```
ESP32 Livestock Management System Started
Device ID: ESP32_001
Location: Barn A
WiFi connected successfully
IP address: 192.168.1.XXX
MPU9250 initialized successfully
Calibrating sensors...
=== Sensor Readings ===
Temperature: 25.3°C
Humidity: 60.2%
Activity Level: 1.2g
Motion Detected: No
=======================
Sending data: {"device_id":"ESP32_001",...}
HTTP Response: 200
```

### 5.2 Verify Database Updates
Check if data is being received:
```sql
-- Check device status
SELECT * FROM esp32_devices WHERE device_id = 'ESP32_001';

-- Check sensor data
SELECT * FROM realtime_sensor_data ORDER BY timestamp DESC LIMIT 10;

-- Check for alerts
SELECT * FROM device_alerts ORDER BY timestamp DESC LIMIT 5;
```

### 5.3 Test Alert System
Trigger alerts by:
1. Breathing on DHT11 (increase humidity)
2. Moving the ESP32 (trigger motion)
3. Covering DHT11 (temperature change)

You should hear buzzer beeps and see alerts in the database.

## Step 6: Multiple Device Setup

### 6.1 For Additional ESP32 Devices
1. Copy the Arduino code
2. Update configuration for each device:
   ```cpp
   #define DEVICE_ID "ESP32_002"     // Unique ID
   #define DEVICE_LOCATION "Barn B"  // Different location
   ```
3. Upload to each ESP32

### 6.2 Device Management
Each device will automatically register in the database when it first connects.

## Step 7: Web Dashboard (Optional)

### 7.1 Create Simple Dashboard
Create `dashboard.php` in your livestock directory:
```php
<?php
require_once 'api/config.php';
$pdo = getDBConnection();

// Get latest sensor data
$stmt = $pdo->query("SELECT * FROM latest_sensor_readings");
$devices = $stmt->fetchAll();

// Get active alerts
$stmt = $pdo->query("SELECT * FROM active_device_alerts LIMIT 10");
$alerts = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Livestock Monitoring Dashboard</title>
    <meta http-equiv="refresh" content="30"> <!-- Auto refresh every 30 seconds -->
</head>
<body>
    <h1>Livestock Monitoring Dashboard</h1>
    
    <h2>Device Status</h2>
    <table border="1">
        <tr>
            <th>Device</th>
            <th>Location</th>
            <th>Temperature</th>
            <th>Humidity</th>
            <th>Activity</th>
            <th>Last Update</th>
            <th>Status</th>
        </tr>
        <?php foreach ($devices as $device): ?>
        <tr>
            <td><?= htmlspecialchars($device['device_name']) ?></td>
            <td><?= htmlspecialchars($device['location']) ?></td>
            <td><?= $device['temperature'] ?>°C</td>
            <td><?= $device['humidity'] ?>%</td>
            <td><?= $device['activity_level'] ?>g</td>
            <td><?= $device['timestamp'] ?></td>
            <td><?= $device['device_status'] ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    
    <h2>Active Alerts</h2>
    <table border="1">
        <tr>
            <th>Device</th>
            <th>Alert Type</th>
            <th>Message</th>
            <th>Severity</th>
            <th>Time</th>
        </tr>
        <?php foreach ($alerts as $alert): ?>
        <tr style="background-color: <?= $alert['severity'] == 'Critical' ? '#ffcccc' : ($alert['severity'] == 'High' ? '#ffffcc' : '#ffffff') ?>">
            <td><?= htmlspecialchars($alert['device_name']) ?></td>
            <td><?= htmlspecialchars($alert['alert_type']) ?></td>
            <td><?= htmlspecialchars($alert['alert_message']) ?></td>
            <td><?= htmlspecialchars($alert['severity']) ?></td>
            <td><?= $alert['timestamp'] ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
</body>
</html>
```

Access the dashboard at: `http://localhost/livestock/dashboard.php`

## Troubleshooting

### Common Issues:

1. **ESP32 won't connect to WiFi**
   - Check SSID and password
   - Ensure 2.4GHz network
   - Check signal strength

2. **HTTP requests fail**
   - Verify server IP address
   - Check if XAMPP is running
   - Test API endpoints manually

3. **Sensor readings are wrong**
   - Check wiring connections
   - Verify power supply (3.3V)
   - Wait for sensor stabilization

4. **Database errors**
   - Check MySQL is running
   - Verify database credentials
   - Run database setup scripts

5. **No alerts generated**
   - Check threshold values
   - Verify alert logic
   - Check database permissions

### Debug Mode:
Enable debug mode in `esp32_config.h`:
```cpp
#define DEBUG_MODE 1
```

This will provide detailed serial output for troubleshooting.

## Maintenance

### Regular Tasks:
1. Clean old sensor data (run monthly):
   ```sql
   CALL CleanOldSensorData(30); -- Keep 30 days
   ```

2. Check device status:
   ```sql
   SELECT * FROM esp32_devices WHERE status = 'Offline';
   ```

3. Review alert patterns:
   ```sql
   SELECT alert_type, COUNT(*) as count 
   FROM device_alerts 
   WHERE timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY)
   GROUP BY alert_type;
   ```

## Support
For issues or questions:
1. Check serial monitor output
2. Verify database logs
3. Test individual components
4. Review configuration settings

The system is now ready for livestock monitoring!
