# ESP32 Hardware Troubleshooting Guide

## 🔧 Current Issues
Your ESP32 shows these symptoms:
- ✅ WiFi connection working
- ✅ HTTP communication working  
- ❌ DHT11 sensor reading -999 values
- ❌ MPU9250 sensor not responding

## 📋 Hardware Checklist

### DHT11 Sensor Wiring
```
DHT11 Pin    →    ESP32 Pin
VCC          →    3.3V or 5V
GND          →    GND  
DATA         →    GPIO2 (D2)
```

**Common DHT11 Issues:**
1. **Loose connections** - Check all wires are firmly connected
2. **Wrong voltage** - DHT11 works with both 3.3V and 5V, try both
3. **Damaged sensor** - DHT11 sensors can fail, try a new one
4. **Missing pull-up resistor** - Add 10kΩ resistor between DATA and VCC
5. **Timing issues** - Need 2+ second delays between readings

### MPU9250 Sensor Wiring  
```
MPU9250 Pin  →    ESP32 Pin
VCC          →    3.3V
GND          →    GND
SDA          →    GPIO18 (or GPIO21)
SCL          →    GPIO19 (or GPIO22)
```

**Common MPU9250 Issues:**
1. **I2C address conflict** - Try both 0x68 and 0x69
2. **Missing pull-up resistors** - Add 4.7kΩ resistors on SDA and SCL lines
3. **Power supply issues** - Ensure stable 3.3V supply
4. **I2C bus speed** - Try lower speeds (100kHz instead of 400kHz)

## 🔍 Step-by-Step Debugging

### Step 1: Test DHT11 Alone
```cpp
#include <DHT.h>
#define DHT_PIN 2
#define DHT_TYPE DHT11
DHT dht(DHT_PIN, DHT_TYPE);

void setup() {
  Serial.begin(115200);
  dht.begin();
}

void loop() {
  float temp = dht.readTemperature();
  float hum = dht.readHumidity();
  
  Serial.printf("Temp: %.1f°C, Humidity: %.1f%%\n", temp, hum);
  Serial.printf("Valid: %s\n", (!isnan(temp) && !isnan(hum)) ? "YES" : "NO");
  
  delay(3000);
}
```

### Step 2: Test MPU9250 Alone
```cpp
#include <Wire.h>
#include <MPU9250.h>

MPU9250 mpu;

void setup() {
  Serial.begin(115200);
  Wire.begin(18, 19); // SDA=18, SCL=19
  
  if (mpu.setup(0x68)) {
    Serial.println("MPU9250 found at 0x68");
  } else if (mpu.setup(0x69)) {
    Serial.println("MPU9250 found at 0x69");
  } else {
    Serial.println("MPU9250 not found!");
  }
}

void loop() {
  if (mpu.update()) {
    Serial.printf("Accel: X=%.2f, Y=%.2f, Z=%.2f\n", 
      mpu.getAccX(), mpu.getAccY(), mpu.getAccZ());
  } else {
    Serial.println("MPU9250 read failed");
  }
  delay(1000);
}
```

### Step 3: I2C Scanner
```cpp
#include <Wire.h>

void setup() {
  Serial.begin(115200);
  Wire.begin(18, 19); // Your I2C pins
  Serial.println("I2C Scanner starting...");
}

void loop() {
  byte error, address;
  int devices = 0;
  
  Serial.println("Scanning I2C bus...");
  
  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.printf("Device found at 0x%02X\n", address);
      devices++;
    }
  }
  
  if (devices == 0) {
    Serial.println("No I2C devices found");
  } else {
    Serial.printf("Found %d devices\n", devices);
  }
  
  delay(5000);
}
```

## 🛠️ Hardware Solutions

### For DHT11 Issues:
1. **Check wiring** - Verify all connections
2. **Add pull-up resistor** - 10kΩ between DATA and VCC
3. **Try different GPIO pin** - Use GPIO4 or GPIO5 instead
4. **Replace sensor** - DHT11 sensors are cheap and can fail
5. **Check power supply** - Ensure stable voltage

### For MPU9250 Issues:
1. **Add pull-up resistors** - 4.7kΩ on both SDA and SCL
2. **Check I2C address** - Use I2C scanner to find correct address
3. **Try different pins** - Use GPIO21/22 for I2C instead
4. **Lower I2C speed** - Set to 100kHz: `Wire.setClock(100000)`
5. **Check power supply** - MPU9250 needs stable 3.3V

### Breadboard Layout Example:
```
ESP32          DHT11          MPU9250
-----          -----          -------
3.3V    -----> VCC     -----> VCC
GND     -----> GND     -----> GND
GPIO2   -----> DATA
GPIO18  ----------------> SDA
GPIO19  ----------------> SCL

Pull-up Resistors:
- 10kΩ: DHT11 DATA to 3.3V
- 4.7kΩ: SDA to 3.3V  
- 4.7kΩ: SCL to 3.3V
```

## 📊 Expected Results

### Working DHT11:
```
Temp: 23.5°C, Humidity: 65.2%
Valid: YES
```

### Working MPU9250:
```
Device found at 0x68
Accel: X=0.02, Y=0.15, Z=0.98
```

### Failed Sensors:
```
Temp: nan°C, Humidity: nan%
Valid: NO
MPU9250 not found!
No I2C devices found
```

## 🚨 Quick Fixes to Try:

1. **Power cycle** - Unplug and reconnect ESP32
2. **Check breadboard** - Ensure no loose connections
3. **Swap sensors** - Try different DHT11/MPU9250 modules
4. **Use different pins** - Try GPIO4, GPIO5 for DHT11
5. **Add delays** - Increase delays between sensor readings
6. **Check voltage** - Measure 3.3V and 5V rails with multimeter

## 📞 If Still Not Working:

1. **Test with Arduino Uno** - Verify sensors work on different board
2. **Check sensor datasheets** - Verify pin configurations
3. **Use oscilloscope** - Check I2C signals if available
4. **Try different libraries** - Some sensors work better with different libraries

The improved ESP32 code above includes better error handling, multiple retry attempts, and detailed debugging output to help identify exactly where the problem is occurring.
