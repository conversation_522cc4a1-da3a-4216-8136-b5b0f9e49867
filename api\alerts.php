<?php
/**
 * ESP32 Alerts API Endpoint
 * Handles alert management for ESP32 devices
 */

require_once 'config.php';

// Log the request
logRequest('alerts', $_POST);

// Validate API key (optional)
validateApiKey();

// Get database connection
$pdo = getDBConnection();

// Handle different HTTP methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        handleAlertPost($pdo);
        break;
    case 'GET':
        handleAlertGet($pdo);
        break;
    case 'PUT':
        handleAlertPut($pdo);
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

/**
 * Handle POST request - create new alert from ESP32
 */
function handleAlertPost($pdo) {
    // Get JSON data from ESP32
    $data = getJsonInput();
    
    // Validate required fields
    $required_fields = ['device_id', 'alert_type', 'alert_message'];
    validateRequiredFields($data, $required_fields);
    
    try {
        // Update device status
        updateDeviceStatus($data['device_id'], $pdo);
        
        // Determine severity based on alert type
        $severity = determineSeverity($data['alert_type']);
        
        // Check if similar alert exists in last 30 minutes to avoid spam
        $stmt = $pdo->prepare("
            SELECT id FROM device_alerts 
            WHERE device_id = ? AND alert_type = ? 
            AND timestamp > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            AND resolved = 0
        ");
        
        $stmt->execute([$data['device_id'], $data['alert_type']]);
        
        if ($stmt->rowCount() == 0) {
            // Insert into device_alerts table
            $stmt = $pdo->prepare("
                INSERT INTO device_alerts 
                (device_id, alert_type, alert_message, severity, location, timestamp) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $location = $data['location'] ?? null;
            
            $stmt->execute([
                $data['device_id'],
                $data['alert_type'],
                $data['alert_message'],
                $severity,
                $location
            ]);
            
            $alert_id = $pdo->lastInsertId();
            
            // Also insert into main alerts table for compatibility
            $stmt2 = $pdo->prepare("
                INSERT INTO alerts 
                (alert_type, alert_message, timestamp) 
                VALUES (?, ?, NOW())
            ");
            
            $stmt2->execute([$data['alert_type'], $data['alert_message']]);
            
            sendResponse([
                'success' => true,
                'message' => 'Alert created successfully',
                'alert_id' => $alert_id,
                'device_id' => $data['device_id']
            ]);
        } else {
            sendResponse([
                'success' => true,
                'message' => 'Similar alert already exists',
                'device_id' => $data['device_id']
            ]);
        }
        
    } catch (PDOException $e) {
        error_log("Database error in alerts.php: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Handle GET request - retrieve alerts
 */
function handleAlertGet($pdo) {
    $device_id = $_GET['device_id'] ?? null;
    $resolved = $_GET['resolved'] ?? null;
    $severity = $_GET['severity'] ?? null;
    $limit = (int)($_GET['limit'] ?? 50);
    $hours = (int)($_GET['hours'] ?? 168); // Default 7 days
    
    try {
        $sql = "
            SELECT 
                da.id,
                da.device_id,
                ed.device_name,
                da.alert_type,
                da.alert_message,
                da.severity,
                da.timestamp,
                da.resolved,
                da.resolved_at,
                da.resolved_by,
                da.location
            FROM device_alerts da
            LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
            WHERE da.timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        ";
        
        $params = [$hours];
        
        if ($device_id) {
            $sql .= " AND da.device_id = ?";
            $params[] = $device_id;
        }
        
        if ($resolved !== null) {
            $sql .= " AND da.resolved = ?";
            $params[] = (int)$resolved;
        }
        
        if ($severity) {
            $sql .= " AND da.severity = ?";
            $params[] = $severity;
        }
        
        $sql .= " ORDER BY da.severity DESC, da.timestamp DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $alerts = $stmt->fetchAll();
        
        // Get summary statistics
        $stats_sql = "
            SELECT 
                COUNT(*) as total_alerts,
                SUM(CASE WHEN resolved = 0 THEN 1 ELSE 0 END) as active_alerts,
                SUM(CASE WHEN severity = 'Critical' AND resolved = 0 THEN 1 ELSE 0 END) as critical_alerts,
                SUM(CASE WHEN severity = 'High' AND resolved = 0 THEN 1 ELSE 0 END) as high_alerts
            FROM device_alerts 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        ";
        
        $stats_params = [$hours];
        
        if ($device_id) {
            $stats_sql .= " AND device_id = ?";
            $stats_params[] = $device_id;
        }
        
        $stats_stmt = $pdo->prepare($stats_sql);
        $stats_stmt->execute($stats_params);
        $stats = $stats_stmt->fetch();
        
        sendResponse([
            'success' => true,
            'alerts' => $alerts,
            'statistics' => $stats,
            'count' => count($alerts)
        ]);
        
    } catch (PDOException $e) {
        error_log("Database error in alerts.php GET: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Handle PUT request - update alert (mark as resolved)
 */
function handleAlertPut($pdo) {
    $data = getJsonInput();
    
    // Validate required fields
    $required_fields = ['alert_id'];
    validateRequiredFields($data, $required_fields);
    
    try {
        $resolved_by = $data['resolved_by'] ?? 'System';
        
        $stmt = $pdo->prepare("
            UPDATE device_alerts 
            SET resolved = 1, resolved_at = NOW(), resolved_by = ?
            WHERE id = ?
        ");
        
        $stmt->execute([$resolved_by, $data['alert_id']]);
        
        if ($stmt->rowCount() > 0) {
            sendResponse([
                'success' => true,
                'message' => 'Alert marked as resolved',
                'alert_id' => $data['alert_id']
            ]);
        } else {
            sendResponse(['error' => 'Alert not found'], 404);
        }
        
    } catch (PDOException $e) {
        error_log("Database error in alerts.php PUT: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Determine alert severity based on alert type
 */
function determineSeverity($alert_type) {
    $critical_alerts = ['Critical Temperature', 'System Failure', 'Device Offline'];
    $high_alerts = ['High Temperature', 'Low Temperature', 'High Humidity', 'Low Humidity'];
    $medium_alerts = ['Low Activity', 'High Activity', 'Motion Detected'];
    
    if (in_array($alert_type, $critical_alerts)) {
        return 'Critical';
    } elseif (in_array($alert_type, $high_alerts)) {
        return 'High';
    } elseif (in_array($alert_type, $medium_alerts)) {
        return 'Medium';
    } else {
        return 'Low';
    }
}
?>
