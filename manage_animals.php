<?php
/**
 * Animal Management - Add, Edit, Delete Animals
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_animal':
            addAnimal($pdo, $_POST);
            break;
        case 'edit_animal':
            editAnimal($pdo, $_POST);
            break;
        case 'delete_animal':
            deleteAnimal($pdo, $_POST['animal_id']);
            break;
    }
    
    // Redirect to prevent form resubmission
    header("Location: manage_animals.php");
    exit;
}

// Get animal for editing if ID provided
$edit_animal = null;
if (isset($_GET['edit'])) {
    $stmt = $pdo->prepare("SELECT * FROM animals WHERE animal_id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_animal = $stmt->fetch();
}

// Get all animals
$animals_stmt = $pdo->query("
    SELECT 
        a.*,
        hr.weight,
        hr.health_status,
        hr.record_date
    FROM animals a
    LEFT JOIN (
        SELECT 
            animal_id,
            weight,
            health_status,
            record_date,
            ROW_NUMBER() OVER (PARTITION BY animal_id ORDER BY record_date DESC) as rn
        FROM health_records
    ) hr ON a.animal_id = hr.animal_id AND hr.rn = 1
    ORDER BY a.animal_id DESC
");
$animals = $animals_stmt->fetchAll();

function addAnimal($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO animals (tag_number, species, breed, gender, birth_date, status, notes) 
            VALUES (?, ?, ?, ?, ?, 'Active', ?)
        ");
        $stmt->execute([
            $data['tag_number'],
            $data['species'],
            $data['breed'],
            $data['gender'],
            $data['birth_date'],
            $data['notes'] ?? ''
        ]);
        $_SESSION['success'] = "Animal added successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error adding animal: " . $e->getMessage();
    }
}

function editAnimal($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            UPDATE animals 
            SET tag_number = ?, species = ?, breed = ?, gender = ?, birth_date = ?, notes = ?
            WHERE animal_id = ?
        ");
        $stmt->execute([
            $data['tag_number'],
            $data['species'],
            $data['breed'],
            $data['gender'],
            $data['birth_date'],
            $data['notes'] ?? '',
            $data['animal_id']
        ]);
        $_SESSION['success'] = "Animal updated successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error updating animal: " . $e->getMessage();
    }
}

function deleteAnimal($pdo, $animal_id) {
    try {
        // Check if animal has health records
        $health_check = $pdo->prepare("SELECT COUNT(*) as count FROM health_records WHERE animal_id = ?");
        $health_check->execute([$animal_id]);
        $health_count = $health_check->fetch()['count'];

        if ($health_count > 0) {
            $_SESSION['error'] = "Cannot delete animal with existing health records. Please remove health records first or mark as inactive.";
            return;
        }

        // Check if animal has any other dependencies
        $dependencies = [];

        // Add more dependency checks here if needed

        if (!empty($dependencies)) {
            $_SESSION['error'] = "Cannot delete animal with existing records: " . implode(', ', $dependencies);
            return;
        }

        // If no dependencies, perform soft delete - change status to Inactive
        $stmt = $pdo->prepare("UPDATE animals SET status = 'Inactive' WHERE animal_id = ?");
        $stmt->execute([$animal_id]);
        $_SESSION['success'] = "Animal marked as inactive successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error deleting animal: " . $e->getMessage();
    }
}

session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Animals - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .btn-success {
            background-color: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background-color: #38a169;
        }

        .btn-danger {
            background-color: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background-color: #e53e3e;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Forms */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4a6cf7;
            box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-inactive {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-healthy {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-sick {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-monitoring {
            background-color: #fbb6ce;
            color: #97266d;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .close-btn:hover {
            color: #374151;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link active">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> AI Insights
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">Manage Animals</h1>
        <button class="btn btn-primary" onclick="openAddModal()">
            <i class="fas fa-plus"></i> Add New Animal
        </button>
    </div>

    <div class="container">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Animals Table -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Animal Records</h3>
            </div>
            <div class="card-body">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tag Number</th>
                            <th>Species</th>
                            <th>Breed</th>
                            <th>Gender</th>
                            <th>Age</th>
                            <th>Weight</th>
                            <th>Health Status</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($animals as $animal): ?>
                        <tr>
                            <td><?= $animal['animal_id'] ?></td>
                            <td><strong><?= htmlspecialchars($animal['tag_number']) ?></strong></td>
                            <td><?= htmlspecialchars($animal['species']) ?></td>
                            <td><?= htmlspecialchars($animal['breed']) ?></td>
                            <td><?= htmlspecialchars($animal['gender']) ?></td>
                            <td>
                                <?php 
                                if ($animal['birth_date']) {
                                    $birth_date = new DateTime($animal['birth_date']);
                                    $now = new DateTime();
                                    $age = $birth_date->diff($now);
                                    echo $age->y . 'y ' . $age->m . 'm';
                                } else {
                                    echo '--';
                                }
                                ?>
                            </td>
                            <td><?= $animal['weight'] ? number_format($animal['weight'], 1) . ' kg' : '--' ?></td>
                            <td>
                                <?php if ($animal['health_status']): ?>
                                    <span class="status-badge status-<?= strtolower($animal['health_status']) ?>">
                                        <?= htmlspecialchars($animal['health_status']) ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: #a0aec0;">No records</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?= strtolower($animal['status']) ?>">
                                    <?= htmlspecialchars($animal['status']) ?>
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editAnimal(<?= htmlspecialchars(json_encode($animal)) ?>)">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="confirmDelete(<?= $animal['animal_id'] ?>, '<?= htmlspecialchars($animal['tag_number']) ?>')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add/Edit Animal Modal -->
    <div id="animalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Add New Animal</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="animalForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add_animal">
                <input type="hidden" name="animal_id" id="animalId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Tag Number *</label>
                        <input type="text" name="tag_number" id="tagNumber" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Species *</label>
                        <select name="species" id="species" class="form-control" required>
                            <option value="">Select Species</option>
                            <option value="Cattle">Cattle</option>
                            <option value="Sheep">Sheep</option>
                            <option value="Goat">Goat</option>
                            <option value="Pig">Pig</option>
                            <option value="Chicken">Chicken</option>
                            <option value="Horse">Horse</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Breed</label>
                        <input type="text" name="breed" id="breed" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Gender *</label>
                        <select name="gender" id="gender" class="form-control" required>
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Birth Date</label>
                        <input type="date" name="birth_date" id="birthDate" class="form-control">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #6b7280; color: white;">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Animal
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
            </div>
            <p>Are you sure you want to delete animal <strong id="deleteAnimalName"></strong>?</p>
            <p style="color: #6b7280; font-size: 0.875rem;">This will mark the animal as inactive. Animals with health records cannot be deleted.</p>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                <button type="button" class="btn" onclick="closeDeleteModal()" style="background: #6b7280; color: white;">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_animal">
                    <input type="hidden" name="animal_id" id="deleteAnimalId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'Add New Animal';
            document.getElementById('formAction').value = 'add_animal';
            document.getElementById('animalForm').reset();
            document.getElementById('animalId').value = '';
            document.getElementById('animalModal').classList.add('show');
        }

        function editAnimal(animal) {
            document.getElementById('modalTitle').textContent = 'Edit Animal';
            document.getElementById('formAction').value = 'edit_animal';
            document.getElementById('animalId').value = animal.animal_id;
            document.getElementById('tagNumber').value = animal.tag_number;
            document.getElementById('species').value = animal.species;
            document.getElementById('breed').value = animal.breed || '';
            document.getElementById('gender').value = animal.gender;
            document.getElementById('birthDate').value = animal.birth_date;
            document.getElementById('notes').value = animal.notes || '';
            document.getElementById('animalModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('animalModal').classList.remove('show');
        }

        function confirmDelete(animalId, tagNumber) {
            document.getElementById('deleteAnimalId').value = animalId;
            document.getElementById('deleteAnimalName').textContent = tagNumber;
            document.getElementById('deleteModal').classList.add('show');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const animalModal = document.getElementById('animalModal');
            const deleteModal = document.getElementById('deleteModal');
            
            if (event.target === animalModal) {
                closeModal();
            }
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
        }
    </script>
</body>
</html>
