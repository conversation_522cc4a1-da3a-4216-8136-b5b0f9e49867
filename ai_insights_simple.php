<?php
/**
 * Simplified AI Insights Page - Works with minimal data
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Simple health trends analysis
try {
    $health_trends_stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_animals,
            COUNT(CASE WHEN status = 'Active' THEN 1 END) as active_animals,
            AVG(TIMESTAMPDIFF(MONTH, birth_date, NOW())) as avg_age_months
        FROM animals
    ");
    $health_trends = $health_trends_stmt->fetch();
} catch (Exception $e) {
    $health_trends = ['total_animals' => 0, 'active_animals' => 0, 'avg_age_months' => 0];
}

// Simple environmental data
try {
    $env_stmt = $pdo->query("
        SELECT 
            COUNT(DISTINCT device_id) as total_devices,
            COUNT(*) as total_readings,
            AVG(temperature) as avg_temp,
            AVG(humidity) as avg_humidity
        FROM realtime_sensor_data 
        WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $env_data = $env_stmt->fetch();
} catch (Exception $e) {
    $env_data = ['total_devices' => 0, 'total_readings' => 0, 'avg_temp' => null, 'avg_humidity' => null];
}

// Simple health records count
try {
    $health_records_stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN record_date > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_records
        FROM health_records
    ");
    $health_records = $health_records_stmt->fetch();
} catch (Exception $e) {
    $health_records = ['total_records' => 0, 'recent_records' => 0];
}

// Simple alerts count
try {
    $alerts_stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_alerts,
            COUNT(CASE WHEN resolved = 0 THEN 1 END) as active_alerts
        FROM device_alerts
    ");
    $alerts_data = $alerts_stmt->fetch();
} catch (Exception $e) {
    $alerts_data = ['total_alerts' => 0, 'active_alerts' => 0];
}

// Generate simple AI insights
$ai_insights = [];

// Data availability insight
if ($health_records['total_records'] < 10) {
    $ai_insights[] = [
        'title' => 'Data Collection Needed',
        'type' => 'info',
        'message' => 'Add more health records to enable advanced AI predictions. Target: 10+ records per animal.',
        'confidence' => 95
    ];
}

// Environmental insight
if ($env_data['total_readings'] > 0) {
    if ($env_data['avg_temp'] > 25) {
        $ai_insights[] = [
            'title' => 'Temperature Monitoring',
            'type' => 'medium',
            'message' => sprintf('Average temperature is %.1f°C. Consider ventilation improvements for animal comfort.', $env_data['avg_temp']),
            'confidence' => 82
        ];
    }
} else {
    $ai_insights[] = [
        'title' => 'IoT Setup Required',
        'type' => 'info',
        'message' => 'Connect ESP32 sensors to enable environmental monitoring and predictions.',
        'confidence' => 90
    ];
}

// Health monitoring insight
if ($health_records['recent_records'] > 0) {
    $ai_insights[] = [
        'title' => 'Health Monitoring Active',
        'type' => 'info',
        'message' => sprintf('Good! %d health records in the last 30 days. Continue regular monitoring for better AI insights.', $health_records['recent_records']),
        'confidence' => 88
    ];
}

// Alert management insight
if ($alerts_data['active_alerts'] > 0) {
    $ai_insights[] = [
        'title' => 'Alert Resolution Needed',
        'type' => 'medium',
        'message' => sprintf('%d active alerts require attention. Resolve alerts to improve system reliability.', $alerts_data['active_alerts']),
        'confidence' => 92
    ];
}

session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Insights (Simple) - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.blue { background: #4a6cf7; }
        .metric-icon.green { background: #48bb78; }
        .metric-icon.purple { background: #9f7aea; }
        .metric-icon.orange { background: #ed8936; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Insight Items */
        .insight-item {
            padding: 1rem;
            border-left: 4px solid #4a6cf7;
            background-color: #f7fafc;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
        }

        .insight-item.medium {
            border-left-color: #ed8936;
            background-color: #fffbf0;
        }

        .insight-item.info {
            border-left-color: #4a6cf7;
            background-color: #ebf8ff;
        }

        .insight-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .insight-message {
            font-size: 0.875rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .insight-meta {
            font-size: 0.75rem;
            color: #718096;
            text-align: right;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .risk-medium {
            background-color: #fbd38d;
            color: #744210;
        }

        .risk-info {
            background-color: #bee3f8;
            color: #2a4365;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link active">
                        <i class="fas fa-chart-line"></i> AI Insights
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">🤖 AI Insights (Simple)</h1>
        <p class="page-subtitle">Basic analytics and recommendations for your livestock management</p>
    </div>

    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon blue">
                    <i class="fas fa-paw"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $health_trends['active_animals'] ?></h3>
                    <p>Active Animals</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $health_records['recent_records'] ?></h3>
                    <p>Recent Health Records</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon purple">
                    <i class="fas fa-wifi"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $env_data['total_devices'] ?></h3>
                    <p>IoT Devices</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon orange">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $alerts_data['active_alerts'] ?></h3>
                    <p>Active Alerts</p>
                </div>
            </div>
        </div>

        <!-- AI Insights -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">💡 AI Recommendations</h3>
            </div>
            <div class="card-body">
                <?php if (empty($ai_insights)): ?>
                    <div style="text-align: center; color: #48bb78; padding: 2rem;">
                        <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p><strong>System Running Smoothly</strong></p>
                        <p style="color: #718096; font-size: 0.9rem;">No immediate recommendations. Continue monitoring your livestock!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($ai_insights as $insight): ?>
                        <div class="insight-item <?= $insight['type'] ?>">
                            <div class="insight-header">
                                <span><?= htmlspecialchars($insight['title']) ?></span>
                                <span class="status-badge risk-<?= $insight['type'] ?>">
                                    <?= ucfirst($insight['type']) ?>
                                </span>
                            </div>
                            <div class="insight-message">
                                <?= htmlspecialchars($insight['message']) ?>
                            </div>
                            <div class="insight-meta">
                                AI Confidence: <?= $insight['confidence'] ?>%
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- System Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📊 System Overview</h3>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <h4 style="color: #2d3748;">Total Animals</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: #4a6cf7;"><?= $health_trends['total_animals'] ?></div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <h4 style="color: #2d3748;">Health Records</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: #48bb78;"><?= $health_records['total_records'] ?></div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <h4 style="color: #2d3748;">Sensor Readings</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: #9f7aea;"><?= $env_data['total_readings'] ?></div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <h4 style="color: #2d3748;">Avg Temperature</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: #ed8936;">
                            <?= $env_data['avg_temp'] ? number_format($env_data['avg_temp'], 1) . '°C' : '--' ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🚀 Next Steps</h3>
            </div>
            <div class="card-body">
                <div style="background: #ebf8ff; padding: 1.5rem; border-radius: 8px;">
                    <h4 style="color: #2a4365; margin-bottom: 1rem;">To unlock advanced AI features:</h4>
                    <ul style="color: #2a4365; margin-left: 1.5rem;">
                        <li>Add more animals using the <a href="manage_animals.php" style="color: #4a6cf7;">Animal Management</a> page</li>
                        <li>Record regular health data via <a href="manage_health.php" style="color: #4a6cf7;">Health Records</a></li>
                        <li>Connect ESP32 sensors for environmental monitoring</li>
                        <li>Maintain at least 10 health records per animal for predictions</li>
                        <li>Try the <a href="ai_insights.php" style="color: #4a6cf7;">Full AI Insights</a> page once you have more data</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div style="text-align: center;">
            <strong>🤖 Simple AI Livestock Management</strong><br>
            <span style="color: #4a6cf7;">Basic analytics and recommendations</span><br>
            <small style="color: #718096;">
                Last updated: <?= date('Y-m-d H:i:s') ?>
            </small>
        </div>
    </div>
</body>
</html>
