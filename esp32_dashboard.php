<?php
/**
 * ESP32 Livestock Management Dashboard
 * Real-time monitoring interface for ESP32 devices
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Get latest sensor data for all devices
$devices_stmt = $pdo->query("
    SELECT
        ed.*,
        lsr.temperature,
        lsr.humidity,
        lsr.activity_level,
        lsr.motion_detected,
        lsr.timestamp as last_reading,
        COUNT(da.id) as active_alerts
    FROM esp32_devices ed
    LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
    LEFT JOIN device_alerts da ON ed.device_id = da.device_id AND da.resolved = 0
    GROUP BY ed.device_id
    ORDER BY ed.device_id
");
$devices = $devices_stmt->fetchAll();

// Get active alerts
$alerts_stmt = $pdo->query("
    SELECT * FROM active_device_alerts
    ORDER BY severity DESC, timestamp DESC
    LIMIT 10
");
$alerts = $alerts_stmt->fetchAll();

// Get system statistics
$stats_stmt = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM esp32_devices) as total_devices,
        (SELECT COUNT(*) FROM esp32_devices WHERE status = 'Online') as online_devices,
        (SELECT COUNT(*) FROM device_alerts WHERE resolved = 0) as active_alerts,
        (SELECT COUNT(*) FROM realtime_sensor_data WHERE timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)) as recent_readings
");
$stats = $stats_stmt->fetch();

// Get animal count from existing animals table
$animals_stmt = $pdo->query("SELECT COUNT(*) as total_animals FROM animals WHERE status = 'Active'");
$animals_count = $animals_stmt->fetch()['total_animals'];

// Get recent health records count
$health_stmt = $pdo->query("SELECT COUNT(*) as recent_health FROM health_records WHERE record_date > DATE_SUB(NOW(), INTERVAL 7 DAY)");
$health_count = $health_stmt->fetch()['recent_health'];

// Get average temperature from last 24 hours
$temp_stmt = $pdo->query("
    SELECT AVG(temperature) as avg_temp
    FROM realtime_sensor_data
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND temperature IS NOT NULL
");
$avg_temp = $temp_stmt->fetch()['avg_temp'];

// Get device status distribution
$device_status_stmt = $pdo->query("
    SELECT status, COUNT(*) as count
    FROM esp32_devices
    GROUP BY status
");
$device_status = $device_status_stmt->fetchAll();

// Get recent sensor data for charts
$chart_data_stmt = $pdo->query("
    SELECT
        device_id,
        location,
        temperature,
        humidity,
        activity_level,
        DATE_FORMAT(timestamp, '%H:%i') as time_label,
        timestamp
    FROM realtime_sensor_data
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ORDER BY timestamp DESC
    LIMIT 100
");
$chart_data = $chart_data_stmt->fetchAll();

// Get latest health records
$latest_health_stmt = $pdo->query("
    SELECT hr.*, a.tag_number, a.species
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    ORDER BY hr.record_date DESC
    LIMIT 5
");
$latest_health = $latest_health_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livestock Manager - Dashboard</title>
    <meta http-equiv="refresh" content="30"> <!-- Auto refresh every 30 seconds -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.blue { background: #4a6cf7; }
        .metric-icon.red { background: #f56565; }
        .metric-icon.green { background: #48bb78; }
        .metric-icon.orange { background: #ed8936; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-offline {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-healthy {
            background-color: #c6f6d5;
            color: #22543d;
        }

        /* Alert Items */
        .alert-item {
            padding: 1rem;
            border-left: 4px solid #e53e3e;
            background-color: #fff5f5;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }

        .alert-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert-system {
            border-left-color: #3182ce;
            background-color: #ebf8ff;
        }

        .alert-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-message {
            font-size: 0.875rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .alert-meta {
            font-size: 0.75rem;
            color: #718096;
            display: flex;
            gap: 1rem;
        }

        /* Health Record Items */
        .health-record {
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            background: #f7fafc;
        }

        .health-record-header {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .health-record-details {
            font-size: 0.875rem;
            color: #4a5568;
        }

        /* Sensor Values */
        .sensor-value {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .temperature {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .humidity {
            background-color: #bee3f8;
            color: #2a4365;
        }

        .activity {
            background-color: #c6f6d5;
            color: #22543d;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        .no-data {
            text-align: center;
            color: #a0aec0;
            font-style: italic;
            padding: 3rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .container {
                padding: 1rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .data-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">Dashboard Overview</h1>
        <p class="page-subtitle">Real-time livestock management insights</p>
    </div>
    </style>
</head>
<body>
    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon blue">
                    <i class="fas fa-paw"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $animals_count ?></h3>
                    <p>Total Animals</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-wifi"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $stats['total_devices'] ?> / <?= $stats['online_devices'] ?></h3>
                    <p>Total / Online Devices</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon red">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $stats['active_alerts'] ?></h3>
                    <p>Active Alerts</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon orange">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $avg_temp ? number_format($avg_temp, 1) . '°C' : '--' ?></h3>
                    <p>Avg Temperature</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Device Status Distribution Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Device Status Overview</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="deviceStatusChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Health Status Overview Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Health Status Overview</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="healthStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Alerts and Latest Health Records -->
        <div class="dashboard-grid">
            <!-- Recent Alerts -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Alerts</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($alerts)): ?>
                        <div class="no-data">No active alerts. All systems are operating normally.</div>
                    <?php else: ?>
                        <?php foreach ($alerts as $alert): ?>
                            <div class="alert-item alert-system">
                                <div class="alert-header">
                                    <span><?= htmlspecialchars($alert['alert_type']) ?></span>
                                    <span class="status-badge"><?= htmlspecialchars($alert['severity']) ?></span>
                                </div>
                                <div class="alert-message">
                                    <?= htmlspecialchars($alert['alert_message']) ?>
                                </div>
                                <div class="alert-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($alert['device_name']) ?></span>
                                    <span><i class="fas fa-clock"></i> <?= date('M j, H:i', strtotime($alert['timestamp'])) ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Latest Health Records -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Latest Health Records</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($latest_health)): ?>
                        <div class="no-data">No recent health records found.</div>
                    <?php else: ?>
                        <?php foreach ($latest_health as $health): ?>
                            <div class="health-record">
                                <div class="health-record-header">
                                    <?= htmlspecialchars($health['tag_number']) ?> (<?= htmlspecialchars($health['species']) ?>)
                                    <span class="status-badge status-<?= strtolower($health['health_status']) ?>">
                                        <?= htmlspecialchars($health['health_status']) ?>
                                    </span>
                                </div>
                                <div class="health-record-details">
                                    Temp: <?= number_format($health['temperature'], 1) ?>°C |
                                    <?= date('M j, Y', strtotime($health['record_date'])) ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- IoT Device Status Table -->
        <div class="card full-width">
            <div class="card-header">
                <h3 class="card-title">IoT Device Status & Sensor Readings</h3>
            </div>
            <div class="card-body">
                <?php if (empty($devices)): ?>
                    <div class="no-data">No ESP32 devices found. Make sure your devices are connected and sending data.</div>
                <?php else: ?>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Temperature</th>
                                <th>Humidity</th>
                                <th>Activity</th>
                                <th>Alerts</th>
                                <th>Last Update</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($devices as $device): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($device['device_name'] ?: $device['device_id']) ?></strong><br>
                                    <small style="color: #718096;"><?= htmlspecialchars($device['device_id']) ?></small>
                                </td>
                                <td><?= htmlspecialchars($device['location']) ?></td>
                                <td>
                                    <span class="status-badge status-<?= strtolower($device['status']) ?>">
                                        <?= htmlspecialchars($device['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($device['temperature'] !== null): ?>
                                        <span class="sensor-value temperature"><?= number_format($device['temperature'], 1) ?>°C</span>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['humidity'] !== null): ?>
                                        <span class="sensor-value humidity"><?= number_format($device['humidity'], 1) ?>%</span>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['activity_level'] !== null): ?>
                                        <span class="sensor-value activity"><?= number_format($device['activity_level'], 2) ?>g</span>
                                        <?php if ($device['motion_detected']): ?>
                                            <br><small style="color: #e53e3e;"><i class="fas fa-circle"></i> Motion</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['active_alerts'] > 0): ?>
                                        <span class="status-badge" style="background: #fed7d7; color: #742a2a;">
                                            <?= $device['active_alerts'] ?> alerts
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge status-online">OK</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['last_reading']): ?>
                                        <small style="color: #718096;"><?= date('M j, H:i', strtotime($device['last_reading'])) ?></small>
                                    <?php else: ?>
                                        <small style="color: #a0aec0;">Never</small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Last updated: <?= date('Y-m-d H:i:s') ?> • Auto-refresh every 30 seconds
    </div>

    <script>
        // Device Status Chart
        const deviceStatusCtx = document.getElementById('deviceStatusChart').getContext('2d');
        const deviceStatusChart = new Chart(deviceStatusCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode(array_column($device_status, 'status')) ?>,
                datasets: [{
                    data: <?= json_encode(array_column($device_status, 'count')) ?>,
                    backgroundColor: ['#48bb78', '#f56565', '#ed8936'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Health Status Chart (Sample data)
        const healthStatusCtx = document.getElementById('healthStatusChart').getContext('2d');
        const healthStatusChart = new Chart(healthStatusCtx, {
            type: 'bar',
            data: {
                labels: ['Healthy', 'Monitoring', 'Sick'],
                datasets: [{
                    data: [8, 2, 0],
                    backgroundColor: ['#48bb78', '#ed8936', '#f56565'],
                    borderWidth: 0,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
