<?php
/**
 * Alerts Management Page
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Handle alert resolution
if ($_POST['action'] ?? '' === 'resolve_alert' && isset($_POST['alert_id'])) {
    $stmt = $pdo->prepare("UPDATE device_alerts SET resolved = 1, resolved_at = NOW(), resolved_by = 'Admin' WHERE id = ?");
    $stmt->execute([$_POST['alert_id']]);
    
    $stmt2 = $pdo->prepare("UPDATE alerts SET resolved = 1 WHERE id = ?");
    $stmt2->execute([$_POST['alert_id']]);
    
    header("Location: alerts.php");
    exit;
}

// Get active alerts
$active_alerts_stmt = $pdo->query("
    SELECT 
        da.*,
        ed.device_name,
        ed.location as device_location
    FROM device_alerts da
    LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
    WHERE da.resolved = 0
    ORDER BY 
        CASE da.severity 
            WHEN 'Critical' THEN 1 
            WHEN 'High' THEN 2 
            WHEN 'Medium' THEN 3 
            ELSE 4 
        END,
        da.timestamp DESC
");
$active_alerts = $active_alerts_stmt->fetchAll();

// Get recent resolved alerts
$resolved_alerts_stmt = $pdo->query("
    SELECT 
        da.*,
        ed.device_name,
        ed.location as device_location
    FROM device_alerts da
    LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
    WHERE da.resolved = 1
    ORDER BY da.resolved_at DESC
    LIMIT 20
");
$resolved_alerts = $resolved_alerts_stmt->fetchAll();

// Get alert statistics
$alert_stats_stmt = $pdo->query("
    SELECT 
        COUNT(CASE WHEN resolved = 0 THEN 1 END) as active_count,
        COUNT(CASE WHEN resolved = 1 AND resolved_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as resolved_today,
        COUNT(CASE WHEN severity = 'Critical' AND resolved = 0 THEN 1 END) as critical_count,
        COUNT(CASE WHEN severity = 'High' AND resolved = 0 THEN 1 END) as high_count
    FROM device_alerts
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY)
");
$alert_stats = $alert_stats_stmt->fetch();

// Get alert trends by type
$alert_trends_stmt = $pdo->query("
    SELECT 
        alert_type,
        COUNT(*) as count,
        severity
    FROM device_alerts
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY alert_type, severity
    ORDER BY count DESC
");
$alert_trends = $alert_trends_stmt->fetchAll();

// Get alerts by device
$alerts_by_device_stmt = $pdo->query("
    SELECT 
        da.device_id,
        ed.device_name,
        ed.location,
        COUNT(*) as alert_count,
        COUNT(CASE WHEN da.resolved = 0 THEN 1 END) as active_count
    FROM device_alerts da
    LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
    WHERE da.timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY da.device_id, ed.device_name, ed.location
    ORDER BY active_count DESC, alert_count DESC
");
$alerts_by_device = $alerts_by_device_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livestock Manager - Alerts</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.red { background: #f56565; }
        .metric-icon.green { background: #48bb78; }
        .metric-icon.orange { background: #ed8936; }
        .metric-icon.purple { background: #9f7aea; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Alert Items */
        .alert-item {
            padding: 1rem;
            border-left: 4px solid #f56565;
            background-color: #fff5f5;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }

        .alert-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert-item.critical {
            border-left-color: #e53e3e;
            background-color: #fff5f5;
        }

        .alert-item.high {
            border-left-color: #f56565;
            background-color: #fff8f0;
        }

        .alert-item.medium {
            border-left-color: #ed8936;
            background-color: #fffbf0;
        }

        .alert-item.low {
            border-left-color: #48bb78;
            background-color: #f0fff4;
        }

        .alert-item.resolved {
            border-left-color: #a0aec0;
            background-color: #f7fafc;
            opacity: 0.8;
        }

        .alert-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-message {
            font-size: 0.875rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .alert-meta {
            font-size: 0.75rem;
            color: #718096;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-actions {
            margin-top: 0.5rem;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .severity-critical {
            background-color: #feb2b2;
            color: #742a2a;
        }

        .severity-high {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .severity-medium {
            background-color: #fbd38d;
            color: #744210;
        }

        .severity-low {
            background-color: #c6f6d5;
            color: #22543d;
        }

        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .btn-success {
            background-color: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background-color: #38a169;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link active">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">Alerts</h1>
        <p class="page-subtitle">Monitor and manage system alerts and notifications</p>
    </div>

    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon red">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $alert_stats['active_count'] ?></h3>
                    <p>Active Alerts</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $alert_stats['resolved_today'] ?></h3>
                    <p>Resolved Today</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon orange">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $alert_stats['critical_count'] ?></h3>
                    <p>Critical Alerts</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon purple">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $alert_stats['high_count'] ?></h3>
                    <p>High Priority</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Active Alerts -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Active Alerts</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($active_alerts)): ?>
                        <div style="text-align: center; color: #48bb78; padding: 2rem;">
                            <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <p>No active alerts. All systems are operating normally!</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($active_alerts as $alert): ?>
                            <div class="alert-item <?= strtolower($alert['severity']) ?>">
                                <div class="alert-header">
                                    <span><?= htmlspecialchars($alert['alert_type']) ?></span>
                                    <span class="status-badge severity-<?= strtolower($alert['severity']) ?>">
                                        <?= htmlspecialchars($alert['severity']) ?>
                                    </span>
                                </div>
                                <div class="alert-message">
                                    <?= htmlspecialchars($alert['alert_message']) ?>
                                </div>
                                <div class="alert-meta">
                                    <span>
                                        <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($alert['device_name'] ?: $alert['device_id']) ?>
                                        • <i class="fas fa-clock"></i> <?= date('M j, H:i', strtotime($alert['timestamp'])) ?>
                                    </span>
                                </div>
                                <div class="alert-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="resolve_alert">
                                        <input type="hidden" name="alert_id" value="<?= $alert['id'] ?>">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-check"></i> Resolve
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Alerts by Device -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Alerts by Device (7 days)</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($alerts_by_device)): ?>
                        <div style="text-align: center; color: #a0aec0; padding: 2rem;">
                            <p>No alerts in the last 7 days</p>
                        </div>
                    <?php else: ?>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Device</th>
                                    <th>Location</th>
                                    <th>Total</th>
                                    <th>Active</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($alerts_by_device as $device): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($device['device_name'] ?: $device['device_id']) ?></strong>
                                    </td>
                                    <td><?= htmlspecialchars($device['location']) ?></td>
                                    <td><?= $device['alert_count'] ?></td>
                                    <td>
                                        <?php if ($device['active_count'] > 0): ?>
                                            <span class="status-badge severity-high"><?= $device['active_count'] ?></span>
                                        <?php else: ?>
                                            <span style="color: #48bb78;">0</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Resolved Alerts -->
        <div class="card full-width">
            <div class="card-header">
                <h3 class="card-title">Recently Resolved Alerts</h3>
            </div>
            <div class="card-body">
                <?php if (empty($resolved_alerts)): ?>
                    <div style="text-align: center; color: #a0aec0; padding: 2rem;">
                        <p>No recently resolved alerts</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($resolved_alerts as $alert): ?>
                        <div class="alert-item resolved">
                            <div class="alert-header">
                                <span><?= htmlspecialchars($alert['alert_type']) ?></span>
                                <span class="status-badge" style="background: #e2e8f0; color: #4a5568;">Resolved</span>
                            </div>
                            <div class="alert-message">
                                <?= htmlspecialchars($alert['alert_message']) ?>
                            </div>
                            <div class="alert-meta">
                                <span>
                                    <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($alert['device_name'] ?: $alert['device_id']) ?>
                                    • <i class="fas fa-clock"></i> <?= date('M j, H:i', strtotime($alert['timestamp'])) ?>
                                </span>
                                <span>
                                    Resolved: <?= date('M j, H:i', strtotime($alert['resolved_at'])) ?>
                                    by <?= htmlspecialchars($alert['resolved_by']) ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Livestock Management System • Last updated: <?= date('Y-m-d H:i:s') ?>
    </div>
</body>
</html>
