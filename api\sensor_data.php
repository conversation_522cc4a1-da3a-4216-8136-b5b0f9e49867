<?php
/**
 * ESP32 Sensor Data API Endpoint
 * Handles receiving sensor data from ESP32 devices
 */

require_once 'config.php';

// Log the request
logRequest('sensor_data', $_POST);

// Validate API key (optional)
validateApiKey();

// Get database connection
$pdo = getDBConnection();

// Handle different HTTP methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        handleSensorDataPost($pdo);
        break;
    case 'GET':
        handleSensorDataGet($pdo);
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

/**
 * Handle POST request - receive sensor data from ESP32
 */
function handleSensorDataPost($pdo) {
    // Get JSON data from ESP32
    $data = getJsonInput();
    
    // Validate required fields
    $required_fields = ['device_id', 'location', 'temperature', 'humidity', 'activity_level'];
    validateRequiredFields($data, $required_fields);
    
    try {
        // Update device status
        updateDeviceStatus($data['device_id'], $pdo);
        
        // Insert into realtime_sensor_data table
        $stmt = $pdo->prepare("
            INSERT INTO realtime_sensor_data 
            (device_id, temperature, humidity, activity_level, motion_detected, location, timestamp) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $motion_detected = isset($data['motion_detected']) ? (int)$data['motion_detected'] : 0;
        
        $stmt->execute([
            $data['device_id'],
            $data['temperature'],
            $data['humidity'],
            $data['activity_level'],
            $motion_detected,
            $data['location']
        ]);
        
        // Also insert into main sensor_data table for historical data
        $stmt2 = $pdo->prepare("
            INSERT INTO sensor_data 
            (device_id, temperature, humidity, activity_level, motion_detected, location, timestamp) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt2->execute([
            $data['device_id'],
            $data['temperature'],
            $data['humidity'],
            $data['activity_level'],
            $motion_detected,
            $data['location']
        ]);
        
        // Check for threshold violations and create alerts
        checkThresholds($data, $pdo);
        
        sendResponse([
            'success' => true,
            'message' => 'Sensor data received successfully',
            'device_id' => $data['device_id'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (PDOException $e) {
        error_log("Database error in sensor_data.php: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Handle GET request - retrieve sensor data
 */
function handleSensorDataGet($pdo) {
    $device_id = $_GET['device_id'] ?? null;
    $location = $_GET['location'] ?? null;
    $limit = (int)($_GET['limit'] ?? 100);
    $hours = (int)($_GET['hours'] ?? 24);
    
    try {
        $sql = "
            SELECT 
                device_id,
                temperature,
                humidity,
                activity_level,
                motion_detected,
                location,
                timestamp
            FROM realtime_sensor_data 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        ";
        
        $params = [$hours];
        
        if ($device_id) {
            $sql .= " AND device_id = ?";
            $params[] = $device_id;
        }
        
        if ($location) {
            $sql .= " AND location = ?";
            $params[] = $location;
        }
        
        $sql .= " ORDER BY timestamp DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'data' => $data,
            'count' => count($data)
        ]);
        
    } catch (PDOException $e) {
        error_log("Database error in sensor_data.php GET: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Check sensor thresholds and create alerts if needed
 */
function checkThresholds($data, $pdo) {
    try {
        // Get thresholds for this device/location
        $stmt = $pdo->prepare("
            SELECT sensor_type, min_value, max_value, alert_enabled
            FROM sensor_thresholds 
            WHERE (device_id = ? OR device_id IS NULL) 
            AND (location = ? OR location IS NULL)
            AND alert_enabled = 1
            ORDER BY device_id DESC
        ");
        
        $stmt->execute([$data['device_id'], $data['location']]);
        $thresholds = $stmt->fetchAll();
        
        foreach ($thresholds as $threshold) {
            $sensor_value = null;
            $alert_type = '';
            $alert_message = '';
            
            switch ($threshold['sensor_type']) {
                case 'temperature':
                    $sensor_value = $data['temperature'];
                    if ($sensor_value < $threshold['min_value']) {
                        $alert_type = 'Low Temperature';
                        $alert_message = "Low temperature detected: {$sensor_value}°C at {$data['location']}";
                    } elseif ($sensor_value > $threshold['max_value']) {
                        $alert_type = 'High Temperature';
                        $alert_message = "High temperature detected: {$sensor_value}°C at {$data['location']}";
                    }
                    break;
                    
                case 'humidity':
                    $sensor_value = $data['humidity'];
                    if ($sensor_value < $threshold['min_value']) {
                        $alert_type = 'Low Humidity';
                        $alert_message = "Low humidity detected: {$sensor_value}% at {$data['location']}";
                    } elseif ($sensor_value > $threshold['max_value']) {
                        $alert_type = 'High Humidity';
                        $alert_message = "High humidity detected: {$sensor_value}% at {$data['location']}";
                    }
                    break;
                    
                case 'activity':
                    $sensor_value = $data['activity_level'];
                    if ($sensor_value < $threshold['min_value']) {
                        $alert_type = 'Low Activity';
                        $alert_message = "Very low activity detected: {$sensor_value}g at {$data['location']}";
                    } elseif ($sensor_value > $threshold['max_value']) {
                        $alert_type = 'High Activity';
                        $alert_message = "Unusual high activity detected: {$sensor_value}g at {$data['location']}";
                    }
                    break;
            }
            
            // Create alert if threshold violated
            if ($alert_type && $alert_message) {
                createAlert($data['device_id'], $alert_type, $alert_message, $data['location'], $pdo);
            }
        }
        
    } catch (PDOException $e) {
        error_log("Error checking thresholds: " . $e->getMessage());
    }
}

/**
 * Create an alert in the database
 */
function createAlert($device_id, $alert_type, $alert_message, $location, $pdo) {
    try {
        // Check if similar alert exists in last 30 minutes to avoid spam
        $stmt = $pdo->prepare("
            SELECT id FROM device_alerts 
            WHERE device_id = ? AND alert_type = ? 
            AND timestamp > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            AND resolved = 0
        ");
        
        $stmt->execute([$device_id, $alert_type]);
        
        if ($stmt->rowCount() == 0) {
            // Create new alert
            $stmt = $pdo->prepare("
                INSERT INTO device_alerts 
                (device_id, alert_type, alert_message, location, severity, timestamp) 
                VALUES (?, ?, ?, ?, 'Medium', NOW())
            ");
            
            $stmt->execute([$device_id, $alert_type, $alert_message, $location]);
            
            // Also insert into main alerts table for compatibility
            $stmt2 = $pdo->prepare("
                INSERT INTO alerts 
                (alert_type, alert_message, timestamp) 
                VALUES (?, ?, NOW())
            ");
            
            $stmt2->execute([$alert_type, $alert_message]);
        }
        
    } catch (PDOException $e) {
        error_log("Error creating alert: " . $e->getMessage());
    }
}
?>
