<?php
/**
 * ESP32 Diagnostics Page
 * Helps diagnose ESP32 sensor issues and data problems
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Get all sensor data including invalid readings
$all_sensor_data_stmt = $pdo->query("
    SELECT 
        device_id,
        temperature,
        humidity,
        activity_level,
        motion_detected,
        timestamp,
        CASE 
            WHEN temperature = -999 OR temperature IS NULL THEN 'Invalid'
            WHEN temperature < -50 OR temperature > 80 THEN 'Out of Range'
            ELSE 'Valid'
        END as temp_status,
        CASE 
            WHEN humidity = -999 OR humidity IS NULL THEN 'Invalid'
            WHEN humidity < 0 OR humidity > 100 THEN 'Out of Range'
            ELSE 'Valid'
        END as humidity_status
    FROM realtime_sensor_data 
    ORDER BY timestamp DESC 
    LIMIT 50
");
$sensor_data = $all_sensor_data_stmt->fetchAll();

// Get device statistics
$device_stats_stmt = $pdo->query("
    SELECT 
        device_id,
        COUNT(*) as total_readings,
        COUNT(CASE WHEN temperature = -999 THEN 1 END) as invalid_temp,
        COUNT(CASE WHEN humidity = -999 THEN 1 END) as invalid_humidity,
        MIN(timestamp) as first_reading,
        MAX(timestamp) as last_reading,
        AVG(CASE WHEN temperature != -999 THEN temperature END) as avg_temp,
        AVG(CASE WHEN humidity != -999 THEN humidity END) as avg_humidity
    FROM realtime_sensor_data 
    GROUP BY device_id
    ORDER BY device_id
");
$device_stats = $device_stats_stmt->fetchAll();

// Get ESP32 devices
$devices_stmt = $pdo->query("SELECT * FROM esp32_devices ORDER BY device_id");
$devices = $devices_stmt->fetchAll();

// Check for common issues
$issues = [];

// Check for devices with no data
foreach ($devices as $device) {
    $has_data = false;
    foreach ($device_stats as $stat) {
        if ($stat['device_id'] === $device['device_id']) {
            $has_data = true;
            break;
        }
    }
    if (!$has_data) {
        $issues[] = [
            'type' => 'No Data',
            'device' => $device['device_id'],
            'message' => 'Device registered but no sensor data received'
        ];
    }
}

// Check for devices with high error rates
foreach ($device_stats as $stat) {
    $error_rate = ($stat['invalid_temp'] + $stat['invalid_humidity']) / ($stat['total_readings'] * 2) * 100;
    if ($error_rate > 50) {
        $issues[] = [
            'type' => 'High Error Rate',
            'device' => $stat['device_id'],
            'message' => sprintf('%.1f%% of readings are invalid (-999 values)', $error_rate)
        ];
    }
}

// Check for stale data
foreach ($device_stats as $stat) {
    $last_reading = new DateTime($stat['last_reading']);
    $now = new DateTime();
    $diff = $now->diff($last_reading);
    $hours_ago = $diff->h + ($diff->days * 24);
    
    if ($hours_ago > 2) {
        $issues[] = [
            'type' => 'Stale Data',
            'device' => $stat['device_id'],
            'message' => sprintf('Last reading was %d hours ago', $hours_ago)
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Diagnostics - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #718096;
            font-size: 1.1rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .issue-item {
            padding: 1rem;
            border-left: 4px solid #f56565;
            background-color: #fff5f5;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
        }

        .issue-item.warning {
            border-left-color: #ed8936;
            background-color: #fffbf0;
        }

        .issue-item.info {
            border-left-color: #4a6cf7;
            background-color: #ebf8ff;
        }

        .issue-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }

        .issue-message {
            font-size: 0.875rem;
            color: #4a5568;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-valid {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-invalid {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-out-of-range {
            background-color: #fbd38d;
            color: #744210;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .troubleshooting {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .troubleshooting h3 {
            color: #234e52;
            margin-bottom: 1rem;
        }

        .troubleshooting ul {
            color: #234e52;
            margin-left: 1.5rem;
        }

        .troubleshooting li {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">🔧 ESP32 Diagnostics</div>
    </nav>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">ESP32 Sensor Diagnostics</h1>
            <p class="page-subtitle">Identify and troubleshoot ESP32 sensor issues</p>
        </div>

        <!-- Issues Summary -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🚨 Detected Issues</h3>
            </div>
            <div class="card-body">
                <?php if (empty($issues)): ?>
                    <div style="text-align: center; color: #48bb78; padding: 2rem;">
                        <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p><strong>No Issues Detected</strong></p>
                        <p style="color: #718096; font-size: 0.9rem;">All ESP32 devices appear to be functioning normally</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($issues as $issue): ?>
                        <div class="issue-item <?= strtolower(str_replace(' ', '-', $issue['type'])) ?>">
                            <div class="issue-header">
                                <?= htmlspecialchars($issue['type']) ?> - <?= htmlspecialchars($issue['device']) ?>
                            </div>
                            <div class="issue-message">
                                <?= htmlspecialchars($issue['message']) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="grid">
            <!-- Device Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📊 Device Statistics</h3>
                </div>
                <div class="card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Device ID</th>
                                <th>Total Readings</th>
                                <th>Invalid Temp</th>
                                <th>Invalid Humidity</th>
                                <th>Last Reading</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($device_stats as $stat): ?>
                            <tr>
                                <td><strong><?= htmlspecialchars($stat['device_id']) ?></strong></td>
                                <td><?= $stat['total_readings'] ?></td>
                                <td>
                                    <?php if ($stat['invalid_temp'] > 0): ?>
                                        <span class="status-badge status-invalid"><?= $stat['invalid_temp'] ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-valid">0</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($stat['invalid_humidity'] > 0): ?>
                                        <span class="status-badge status-invalid"><?= $stat['invalid_humidity'] ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-valid">0</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php 
                                    $last = new DateTime($stat['last_reading']);
                                    $now = new DateTime();
                                    $diff = $now->diff($last);
                                    $hours = $diff->h + ($diff->days * 24);
                                    ?>
                                    <?php if ($hours < 2): ?>
                                        <span style="color: #48bb78;"><?= $last->format('H:i') ?></span>
                                    <?php else: ?>
                                        <span style="color: #f56565;"><?= $hours ?>h ago</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Sensor Data -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📡 Recent Sensor Data</h3>
                </div>
                <div class="card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Temperature</th>
                                <th>Humidity</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($sensor_data, 0, 10) as $data): ?>
                            <tr>
                                <td><?= htmlspecialchars($data['device_id']) ?></td>
                                <td>
                                    <?php if ($data['temp_status'] === 'Valid'): ?>
                                        <span class="status-badge status-valid"><?= number_format($data['temperature'], 1) ?>°C</span>
                                    <?php elseif ($data['temp_status'] === 'Invalid'): ?>
                                        <span class="status-badge status-invalid">-999°C</span>
                                    <?php else: ?>
                                        <span class="status-badge status-out-of-range"><?= number_format($data['temperature'], 1) ?>°C</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($data['humidity_status'] === 'Valid'): ?>
                                        <span class="status-badge status-valid"><?= number_format($data['humidity'], 1) ?>%</span>
                                    <?php elseif ($data['humidity_status'] === 'Invalid'): ?>
                                        <span class="status-badge status-invalid">-999%</span>
                                    <?php else: ?>
                                        <span class="status-badge status-out-of-range"><?= number_format($data['humidity'], 1) ?>%</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('H:i:s', strtotime($data['timestamp'])) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Troubleshooting Guide -->
        <div class="troubleshooting">
            <h3>🔧 ESP32 Troubleshooting Guide</h3>
            
            <h4>Common Issues & Solutions:</h4>
            <ul>
                <li><strong>-999 Temperature/Humidity Values:</strong>
                    <ul>
                        <li>Check DHT11 sensor wiring (VCC, GND, Data pin)</li>
                        <li>Verify DHT11 sensor is not damaged</li>
                        <li>Ensure proper power supply (3.3V or 5V)</li>
                        <li>Add delay between readings (2+ seconds)</li>
                    </ul>
                </li>
                <li><strong>No Motion/Activity Data:</strong>
                    <ul>
                        <li>Check MPU9250 I2C connections (SDA, SCL)</li>
                        <li>Verify I2C address (0x68 or 0x69)</li>
                        <li>Check for I2C pull-up resistors</li>
                    </ul>
                </li>
                <li><strong>No Data Received:</strong>
                    <ul>
                        <li>Check WiFi connection and credentials</li>
                        <li>Verify API endpoint URL</li>
                        <li>Check ESP32 power supply</li>
                        <li>Monitor serial output for error messages</li>
                    </ul>
                </li>
                <li><strong>Intermittent Data:</strong>
                    <ul>
                        <li>Check power supply stability</li>
                        <li>Verify WiFi signal strength</li>
                        <li>Add error handling and retry logic</li>
                    </ul>
                </li>
            </ul>

            <h4>Recommended ESP32 Code Improvements:</h4>
            <ul>
                <li>Add sensor validation before sending data</li>
                <li>Implement retry logic for failed readings</li>
                <li>Use watchdog timer for automatic reset</li>
                <li>Add status LED indicators</li>
                <li>Log errors to serial monitor</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="esp32_dashboard.php" style="display: inline-block; padding: 0.75rem 1.5rem; background: #4a6cf7; color: white; text-decoration: none; border-radius: 8px;">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="manage_devices.php" style="display: inline-block; padding: 0.75rem 1.5rem; background: #48bb78; color: white; text-decoration: none; border-radius: 8px; margin-left: 1rem;">
                <i class="fas fa-cog"></i> Manage Devices
            </a>
        </div>
    </div>
</body>
</html>
