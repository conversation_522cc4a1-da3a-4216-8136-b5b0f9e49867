<?php
/**
 * ESP32 Device Management - Add, Edit, Delete Devices
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_device':
            addDevice($pdo, $_POST);
            break;
        case 'edit_device':
            editDevice($pdo, $_POST);
            break;
        case 'delete_device':
            deleteDevice($pdo, $_POST['device_id']);
            break;
        case 'resolve_alert':
            resolveAlert($pdo, $_POST['alert_id']);
            break;
    }
    
    // Redirect to prevent form resubmission
    header("Location: manage_devices.php");
    exit;
}

// Get device for editing if ID provided
$edit_device = null;
if (isset($_GET['edit'])) {
    $stmt = $pdo->prepare("SELECT * FROM esp32_devices WHERE device_id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_device = $stmt->fetch();
}

// Get all devices with latest sensor data
$devices_stmt = $pdo->query("
    SELECT 
        ed.*,
        lsr.temperature,
        lsr.humidity,
        lsr.activity_level,
        lsr.motion_detected,
        lsr.timestamp as last_reading,
        COUNT(da.id) as active_alerts
    FROM esp32_devices ed
    LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
    LEFT JOIN device_alerts da ON ed.device_id = da.device_id AND da.resolved = 0
    GROUP BY ed.device_id
    ORDER BY ed.device_id
");
$devices = $devices_stmt->fetchAll();

// Get active alerts
$alerts_stmt = $pdo->query("
    SELECT da.*, ed.device_name 
    FROM device_alerts da
    LEFT JOIN esp32_devices ed ON da.device_id = ed.device_id
    WHERE da.resolved = 0
    ORDER BY da.severity DESC, da.timestamp DESC
    LIMIT 10
");
$alerts = $alerts_stmt->fetchAll();

function addDevice($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO esp32_devices (device_id, device_name, location, status) 
            VALUES (?, ?, ?, 'Offline')
        ");
        $stmt->execute([
            $data['device_id'],
            $data['device_name'],
            $data['location']
        ]);
        $_SESSION['success'] = "Device added successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error adding device: " . $e->getMessage();
    }
}

function editDevice($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            UPDATE esp32_devices 
            SET device_name = ?, location = ?
            WHERE device_id = ?
        ");
        $stmt->execute([
            $data['device_name'],
            $data['location'],
            $data['device_id']
        ]);
        $_SESSION['success'] = "Device updated successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error updating device: " . $e->getMessage();
    }
}

function deleteDevice($pdo, $device_id) {
    try {
        // Delete related alerts first
        $stmt = $pdo->prepare("DELETE FROM device_alerts WHERE device_id = ?");
        $stmt->execute([$device_id]);
        
        // Delete sensor data
        $stmt = $pdo->prepare("DELETE FROM realtime_sensor_data WHERE device_id = ?");
        $stmt->execute([$device_id]);
        
        // Delete device
        $stmt = $pdo->prepare("DELETE FROM esp32_devices WHERE device_id = ?");
        $stmt->execute([$device_id]);
        
        $_SESSION['success'] = "Device deleted successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error deleting device: " . $e->getMessage();
    }
}

function resolveAlert($pdo, $alert_id) {
    try {
        $stmt = $pdo->prepare("
            UPDATE device_alerts 
            SET resolved = 1, resolved_at = NOW(), resolved_by = 'Admin'
            WHERE id = ?
        ");
        $stmt->execute([$alert_id]);
        $_SESSION['success'] = "Alert resolved successfully!";
    } catch (Exception $e) {
        $_SESSION['error'] = "Error resolving alert: " . $e->getMessage();
    }
}

session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Devices - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .btn-success {
            background-color: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background-color: #38a169;
        }

        .btn-danger {
            background-color: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background-color: #e53e3e;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Forms */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4a6cf7;
            box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-offline {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-error {
            background-color: #fbb6ce;
            color: #97266d;
        }

        /* Sensor Values */
        .sensor-value {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .temperature {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .humidity {
            background-color: #bee3f8;
            color: #2a4365;
        }

        .activity {
            background-color: #c6f6d5;
            color: #22543d;
        }

        /* Alert Items */
        .alert-item {
            padding: 1rem;
            border-left: 4px solid #f56565;
            background-color: #fff5f5;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
        }

        .alert-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-message {
            font-size: 0.875rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .alert-meta {
            font-size: 0.75rem;
            color: #718096;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }

        .close-btn:hover {
            color: #374151;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link active">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> AI Insights
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">Manage ESP32 Devices</h1>
        <div>
            <a href="iot_data.php" class="btn" style="background: #6b7280; color: white; margin-right: 1rem;">
                <i class="fas fa-arrow-left"></i> Back to IoT Data
            </a>
            <button class="btn btn-primary" onclick="openAddModal()">
                <i class="fas fa-plus"></i> Add New Device
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Devices Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ESP32 Devices</h3>
                </div>
                <div class="card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Device ID</th>
                                <th>Name</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Temperature</th>
                                <th>Humidity</th>
                                <th>Activity</th>
                                <th>Alerts</th>
                                <th>Last Update</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($devices as $device): ?>
                            <tr>
                                <td><strong><?= htmlspecialchars($device['device_id']) ?></strong></td>
                                <td><?= htmlspecialchars($device['device_name']) ?></td>
                                <td><?= htmlspecialchars($device['location']) ?></td>
                                <td>
                                    <span class="status-badge status-<?= strtolower($device['status']) ?>">
                                        <?= htmlspecialchars($device['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($device['temperature'] !== null): ?>
                                        <span class="sensor-value temperature"><?= number_format($device['temperature'], 1) ?>°C</span>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['humidity'] !== null): ?>
                                        <span class="sensor-value humidity"><?= number_format($device['humidity'], 1) ?>%</span>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['activity_level'] !== null): ?>
                                        <span class="sensor-value activity"><?= number_format($device['activity_level'], 2) ?>g</span>
                                    <?php else: ?>
                                        <span style="color: #a0aec0;">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['active_alerts'] > 0): ?>
                                        <span class="status-badge status-error"><?= $device['active_alerts'] ?> alerts</span>
                                    <?php else: ?>
                                        <span class="status-badge status-online">OK</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($device['last_reading']): ?>
                                        <small style="color: #718096;"><?= date('M j, H:i', strtotime($device['last_reading'])) ?></small>
                                    <?php else: ?>
                                        <small style="color: #a0aec0;">Never</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="editDevice(<?= htmlspecialchars(json_encode($device)) ?>)">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="confirmDelete('<?= htmlspecialchars($device['device_id']) ?>', '<?= htmlspecialchars($device['device_name']) ?>')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Active Alerts -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Active Device Alerts</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($alerts)): ?>
                        <div style="text-align: center; color: #48bb78; padding: 2rem;">
                            <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <p>No active alerts!</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($alerts as $alert): ?>
                            <div class="alert-item">
                                <div class="alert-header">
                                    <span><?= htmlspecialchars($alert['alert_type']) ?></span>
                                    <span class="status-badge" style="background: #fed7d7; color: #742a2a;">
                                        <?= htmlspecialchars($alert['severity']) ?>
                                    </span>
                                </div>
                                <div class="alert-message">
                                    <?= htmlspecialchars($alert['alert_message']) ?>
                                </div>
                                <div class="alert-meta">
                                    <span>
                                        <i class="fas fa-microchip"></i> <?= htmlspecialchars($alert['device_name'] ?: $alert['device_id']) ?>
                                        • <i class="fas fa-clock"></i> <?= date('M j, H:i', strtotime($alert['timestamp'])) ?>
                                    </span>
                                </div>
                                <div style="margin-top: 0.5rem;">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="resolve_alert">
                                        <input type="hidden" name="alert_id" value="<?= $alert['id'] ?>">
                                        <button type="submit" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> Resolve
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Device Modal -->
    <div id="deviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Add New Device</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="deviceForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add_device">
                <input type="hidden" name="original_device_id" id="originalDeviceId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Device ID *</label>
                        <input type="text" name="device_id" id="deviceId" class="form-control" required placeholder="ESP32_001">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Device Name *</label>
                        <input type="text" name="device_name" id="deviceName" class="form-control" required placeholder="Barn A Sensor">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">Location *</label>
                        <input type="text" name="location" id="location" class="form-control" required placeholder="Barn A - North Corner">
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                    <button type="button" class="btn" onclick="closeModal()" style="background: #6b7280; color: white;">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Device
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
            </div>
            <p>Are you sure you want to delete device <strong id="deleteDeviceName"></strong>?</p>
            <p style="color: #6b7280; font-size: 0.875rem;">This will also delete all sensor data and alerts for this device.</p>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                <button type="button" class="btn" onclick="closeDeleteModal()" style="background: #6b7280; color: white;">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_device">
                    <input type="hidden" name="device_id" id="deleteDeviceId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'Add New Device';
            document.getElementById('formAction').value = 'add_device';
            document.getElementById('deviceForm').reset();
            document.getElementById('originalDeviceId').value = '';
            document.getElementById('deviceId').disabled = false;
            document.getElementById('deviceModal').classList.add('show');
        }

        function editDevice(device) {
            document.getElementById('modalTitle').textContent = 'Edit Device';
            document.getElementById('formAction').value = 'edit_device';
            document.getElementById('originalDeviceId').value = device.device_id;
            document.getElementById('deviceId').value = device.device_id;
            document.getElementById('deviceId').disabled = true; // Can't change device ID
            document.getElementById('deviceName').value = device.device_name;
            document.getElementById('location').value = device.location;
            document.getElementById('deviceModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('deviceModal').classList.remove('show');
        }

        function confirmDelete(deviceId, deviceName) {
            document.getElementById('deleteDeviceId').value = deviceId;
            document.getElementById('deleteDeviceName').textContent = deviceName || deviceId;
            document.getElementById('deleteModal').classList.add('show');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const deviceModal = document.getElementById('deviceModal');
            const deleteModal = document.getElementById('deleteModal');
            
            if (event.target === deviceModal) {
                closeModal();
            }
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
        }
    </script>
</body>
</html>
