<?php
/**
 * IoT Data Management Page
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Get ESP32 devices with latest readings
$devices_stmt = $pdo->query("
    SELECT 
        ed.*,
        lsr.temperature,
        lsr.humidity,
        lsr.activity_level,
        lsr.motion_detected,
        lsr.timestamp as last_reading
    FROM esp32_devices ed
    LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
    ORDER BY ed.device_id
");
$devices = $devices_stmt->fetchAll();

// Get recent sensor data for charts (last 24 hours)
$sensor_data_stmt = $pdo->query("
    SELECT 
        device_id,
        location,
        temperature,
        humidity,
        activity_level,
        motion_detected,
        DATE_FORMAT(timestamp, '%H:%i') as time_label,
        timestamp
    FROM realtime_sensor_data 
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ORDER BY timestamp DESC
    LIMIT 200
");
$sensor_data = $sensor_data_stmt->fetchAll();

// Get IoT statistics
$iot_stats_stmt = $pdo->query("
    SELECT
        COUNT(DISTINCT ed.device_id) as total_devices,
        COUNT(CASE WHEN ed.status = 'Online' THEN 1 END) as online_devices,
        COUNT(rsd.id) as total_readings,
        AVG(CASE WHEN rsd.temperature IS NOT NULL THEN rsd.temperature END) as avg_temperature,
        AVG(CASE WHEN rsd.humidity IS NOT NULL THEN rsd.humidity END) as avg_humidity
    FROM esp32_devices ed
    LEFT JOIN realtime_sensor_data rsd ON ed.device_id = rsd.device_id
    WHERE rsd.timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR) OR rsd.timestamp IS NULL
");
$iot_stats = $iot_stats_stmt->fetch();

// Get location-wise data
$location_data_stmt = $pdo->query("
    SELECT 
        location,
        COUNT(*) as reading_count,
        AVG(temperature) as avg_temp,
        AVG(humidity) as avg_humidity,
        AVG(activity_level) as avg_activity
    FROM realtime_sensor_data 
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND location IS NOT NULL
    GROUP BY location
");
$location_data = $location_data_stmt->fetchAll();

// Get hourly temperature trends for chart
$temp_trends_stmt = $pdo->query("
    SELECT 
        DATE_FORMAT(timestamp, '%H:00') as hour,
        AVG(temperature) as avg_temp,
        location
    FROM realtime_sensor_data 
    WHERE timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND temperature IS NOT NULL
    GROUP BY DATE_FORMAT(timestamp, '%H:00'), location
    ORDER BY hour
");
$temp_trends = $temp_trends_stmt->fetchAll();

// Get device alerts count
$device_alerts_stmt = $pdo->query("
    SELECT
        da.device_id,
        COUNT(*) as alert_count
    FROM device_alerts da
    WHERE da.timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND da.resolved = 0
    GROUP BY da.device_id
");
$device_alerts = $device_alerts_stmt->fetchAll();
$alerts_by_device = array_column($device_alerts, 'alert_count', 'device_id');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livestock Manager - IoT Data</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.blue { background: #4a6cf7; }
        .metric-icon.green { background: #48bb78; }
        .metric-icon.orange { background: #ed8936; }
        .metric-icon.purple { background: #9f7aea; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-offline {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-error {
            background-color: #fbb6ce;
            color: #97266d;
        }

        /* Sensor Values */
        .sensor-value {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .temperature {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .humidity {
            background-color: #bee3f8;
            color: #2a4365;
        }

        .activity {
            background-color: #c6f6d5;
            color: #22543d;
        }

        /* Location Cards */
        .location-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .location-card {
            background: #f7fafc;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #4a6cf7;
        }

        .location-header {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .location-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .location-stat {
            text-align: center;
        }

        .location-stat-value {
            font-weight: 600;
            color: #4a6cf7;
        }

        .location-stat-label {
            color: #718096;
            font-size: 0.75rem;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link active">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">IoT Data</h1>
            <p class="page-subtitle">Monitor ESP32 sensors and environmental data</p>
        </div>
        <div>
            <a href="esp32_diagnostics.php" class="btn" style="background: #ed8936; color: white; margin-right: 1rem;">
                <i class="fas fa-stethoscope"></i> Diagnostics
            </a>
            <a href="manage_devices.php" class="btn btn-primary">
                <i class="fas fa-cog"></i> Manage Devices
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon blue">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $iot_stats['total_devices'] ?></h3>
                    <p>Total Devices</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-wifi"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $iot_stats['online_devices'] ?></h3>
                    <p>Online Devices</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon orange">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $iot_stats['avg_temperature'] ? number_format($iot_stats['avg_temperature'], 1) . '°C' : '--' ?></h3>
                    <p>Avg Temperature</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon purple">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $iot_stats['avg_humidity'] ? number_format($iot_stats['avg_humidity'], 1) . '%' : '--' ?></h3>
                    <p>Avg Humidity</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Temperature Trends Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Temperature Trends (24h)</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="temperatureChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Location Overview -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Location Overview</h3>
                </div>
                <div class="card-body">
                    <div class="location-grid">
                        <?php foreach ($location_data as $location): ?>
                            <div class="location-card">
                                <div class="location-header">
                                    <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($location['location']) ?>
                                </div>
                                <div class="location-stats">
                                    <div class="location-stat">
                                        <div class="location-stat-value"><?= number_format($location['avg_temp'], 1) ?>°C</div>
                                        <div class="location-stat-label">Temperature</div>
                                    </div>
                                    <div class="location-stat">
                                        <div class="location-stat-value"><?= number_format($location['avg_humidity'], 1) ?>%</div>
                                        <div class="location-stat-label">Humidity</div>
                                    </div>
                                    <div class="location-stat">
                                        <div class="location-stat-value"><?= number_format($location['avg_activity'], 2) ?>g</div>
                                        <div class="location-stat-label">Activity</div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Status Table -->
        <div class="card full-width">
            <div class="card-header">
                <h3 class="card-title">ESP32 Device Status</h3>
            </div>
            <div class="card-body">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Device</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Temperature</th>
                            <th>Humidity</th>
                            <th>Activity</th>
                            <th>Motion</th>
                            <th>Alerts</th>
                            <th>Last Update</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($devices as $device): ?>
                        <tr>
                            <td>
                                <strong><?= htmlspecialchars($device['device_name'] ?: $device['device_id']) ?></strong><br>
                                <small style="color: #718096;"><?= htmlspecialchars($device['device_id']) ?></small>
                            </td>
                            <td><?= htmlspecialchars($device['location']) ?></td>
                            <td>
                                <span class="status-badge status-<?= strtolower($device['status']) ?>">
                                    <?= htmlspecialchars($device['status']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($device['temperature'] !== null): ?>
                                    <span class="sensor-value temperature"><?= number_format($device['temperature'], 1) ?>°C</span>
                                <?php else: ?>
                                    <span style="color: #a0aec0;">--</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($device['humidity'] !== null): ?>
                                    <span class="sensor-value humidity"><?= number_format($device['humidity'], 1) ?>%</span>
                                <?php else: ?>
                                    <span style="color: #a0aec0;">--</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($device['activity_level'] !== null): ?>
                                    <span class="sensor-value activity"><?= number_format($device['activity_level'], 2) ?>g</span>
                                <?php else: ?>
                                    <span style="color: #a0aec0;">--</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($device['motion_detected']): ?>
                                    <span style="color: #e53e3e;"><i class="fas fa-circle"></i> Active</span>
                                <?php else: ?>
                                    <span style="color: #a0aec0;">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                $alert_count = $alerts_by_device[$device['device_id']] ?? 0;
                                if ($alert_count > 0): 
                                ?>
                                    <span class="status-badge status-error"><?= $alert_count ?> alerts</span>
                                <?php else: ?>
                                    <span class="status-badge status-online">OK</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($device['last_reading']): ?>
                                    <small style="color: #718096;"><?= date('M j, H:i', strtotime($device['last_reading'])) ?></small>
                                <?php else: ?>
                                    <small style="color: #a0aec0;">Never</small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Livestock Management System • Last updated: <?= date('Y-m-d H:i:s') ?>
    </div>

    <script>
        // Temperature Trends Chart
        const temperatureCtx = document.getElementById('temperatureChart').getContext('2d');
        
        // Process data for chart
        const tempData = <?= json_encode($temp_trends) ?>;
        const locations = [...new Set(tempData.map(d => d.location))];
        const hours = [...new Set(tempData.map(d => d.hour))].sort();
        
        const datasets = locations.map((location, index) => {
            const colors = ['#4a6cf7', '#48bb78', '#ed8936', '#9f7aea', '#f56565'];
            const locationData = tempData.filter(d => d.location === location);
            
            return {
                label: location,
                data: hours.map(hour => {
                    const dataPoint = locationData.find(d => d.hour === hour);
                    return dataPoint ? parseFloat(dataPoint.avg_temp) : null;
                }),
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                fill: false,
                tension: 0.4
            };
        });

        const temperatureChart = new Chart(temperatureCtx, {
            type: 'line',
            data: {
                labels: hours,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Temperature (°C)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time (24h)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
