<?php
/**
 * Health Management Page
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Get recent health records with animal details
$health_records_stmt = $pdo->query("
    SELECT 
        hr.*,
        a.tag_number,
        a.species,
        a.breed,
        a.gender
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    WHERE a.status = 'Active'
    ORDER BY hr.record_date DESC
    LIMIT 50
");
$health_records = $health_records_stmt->fetchAll();

// Get health statistics
$health_stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN record_date > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_records,
        COUNT(CASE WHEN health_status = 'Healthy' THEN 1 END) as healthy_count,
        COUNT(CASE WHEN health_status IN ('Sick', 'Critical') THEN 1 END) as sick_count,
        AVG(temperature) as avg_temperature
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    WHERE a.status = 'Active'
    AND hr.record_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
");
$health_stats = $health_stats_stmt->fetch();

// Get health status distribution
$status_distribution_stmt = $pdo->query("
    SELECT 
        health_status,
        COUNT(*) as count
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    WHERE a.status = 'Active'
    AND hr.record_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY health_status
    ORDER BY count DESC
");
$status_distribution = $status_distribution_stmt->fetchAll();

// Get weight trends (last 6 months)
$weight_trends_stmt = $pdo->query("
    SELECT 
        DATE_FORMAT(record_date, '%Y-%m') as month,
        AVG(weight) as avg_weight
    FROM health_records hr
    JOIN animals a ON hr.animal_id = a.animal_id
    WHERE a.status = 'Active'
    AND hr.record_date > DATE_SUB(NOW(), INTERVAL 6 MONTH)
    AND hr.weight IS NOT NULL
    GROUP BY DATE_FORMAT(record_date, '%Y-%m')
    ORDER BY month
");
$weight_trends = $weight_trends_stmt->fetchAll();

// Get animals needing attention
$attention_needed_stmt = $pdo->query("
    SELECT 
        a.animal_id,
        a.tag_number,
        a.species,
        hr.health_status,
        hr.record_date,
        hr.notes,
        DATEDIFF(NOW(), hr.record_date) as days_since_check
    FROM animals a
    LEFT JOIN (
        SELECT 
            animal_id,
            health_status,
            record_date,
            notes,
            ROW_NUMBER() OVER (PARTITION BY animal_id ORDER BY record_date DESC) as rn
        FROM health_records
    ) hr ON a.animal_id = hr.animal_id AND hr.rn = 1
    WHERE a.status = 'Active'
    AND (
        hr.health_status IN ('Sick', 'Critical', 'Monitoring') 
        OR hr.record_date IS NULL 
        OR hr.record_date < DATE_SUB(NOW(), INTERVAL 30 DAY)
    )
    ORDER BY 
        CASE 
            WHEN hr.health_status = 'Critical' THEN 1
            WHEN hr.health_status = 'Sick' THEN 2
            WHEN hr.health_status = 'Monitoring' THEN 3
            WHEN hr.record_date IS NULL THEN 4
            ELSE 5
        END,
        hr.record_date ASC
    LIMIT 10
");
$attention_needed = $attention_needed_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livestock Manager - Health</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.green { background: #48bb78; }
        .metric-icon.blue { background: #4a6cf7; }
        .metric-icon.red { background: #f56565; }
        .metric-icon.orange { background: #ed8936; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-healthy {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-monitoring {
            background-color: #fbb6ce;
            color: #97266d;
        }

        .status-sick {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-critical {
            background-color: #feb2b2;
            color: #742a2a;
        }

        .status-good {
            background-color: #bee3f8;
            color: #2a4365;
        }

        /* Alert Items */
        .attention-item {
            padding: 1rem;
            border-left: 4px solid #f56565;
            background-color: #fff5f5;
            margin-bottom: 0.75rem;
            border-radius: 0 8px 8px 0;
        }

        .attention-item.critical {
            border-left-color: #e53e3e;
        }

        .attention-item.sick {
            border-left-color: #f56565;
        }

        .attention-item.monitoring {
            border-left-color: #ed8936;
        }

        .attention-item.overdue {
            border-left-color: #a0aec0;
        }

        .attention-header {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }

        .attention-details {
            font-size: 0.875rem;
            color: #4a5568;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link active">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">Health</h1>
            <p class="page-subtitle">Monitor animal health and medical records</p>
        </div>
        <a href="manage_health.php" class="btn btn-primary">
            <i class="fas fa-cog"></i> Manage Health Records
        </a>
    </div>

    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $health_stats['healthy_count'] ?></h3>
                    <p>Healthy Animals</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon blue">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $health_stats['recent_records'] ?></h3>
                    <p>Recent Records (7 days)</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon red">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $health_stats['sick_count'] ?></h3>
                    <p>Need Attention</p>
                </div>
            </div>

        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Health Status Distribution -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Health Status Distribution</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="healthStatusChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Animals Needing Attention -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Animals Needing Attention</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($attention_needed)): ?>
                        <div style="text-align: center; color: #48bb78; padding: 2rem;">
                            <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <p>All animals are healthy!</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($attention_needed as $animal): ?>
                            <div class="attention-item <?= strtolower($animal['health_status'] ?: 'overdue') ?>">
                                <div class="attention-header">
                                    <?= htmlspecialchars($animal['tag_number']) ?> (<?= htmlspecialchars($animal['species']) ?>)
                                    <?php if ($animal['health_status']): ?>
                                        <span class="status-badge status-<?= strtolower($animal['health_status']) ?>">
                                            <?= htmlspecialchars($animal['health_status']) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="attention-details">
                                    <?php if ($animal['record_date']): ?>
                                        Last check: <?= date('M j, Y', strtotime($animal['record_date'])) ?> 
                                        (<?= $animal['days_since_check'] ?> days ago)
                                    <?php else: ?>
                                        No health records found
                                    <?php endif; ?>
                                    <?php if ($animal['notes']): ?>
                                        <br>Notes: <?= htmlspecialchars($animal['notes']) ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Health Records -->
        <div class="card full-width">
            <div class="card-header">
                <h3 class="card-title">Recent Health Records</h3>
            </div>
            <div class="card-body">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Animal</th>
                            <th>Species</th>
                            <th>Temperature</th>
                            <th>Health Status</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($health_records as $record): ?>
                        <tr>
                            <td><?= date('M j, Y', strtotime($record['record_date'])) ?></td>
                            <td>
                                <strong><?= htmlspecialchars($record['tag_number']) ?></strong>
                            </td>
                            <td><?= htmlspecialchars($record['species']) ?></td>
                            <td><?= $record['temperature'] ? number_format($record['temperature'], 1) . '°C' : '--' ?></td>
                            <td>
                                <span class="status-badge status-<?= strtolower($record['health_status']) ?>">
                                    <?= htmlspecialchars($record['health_status']) ?>
                                </span>
                            </td>
                            <td>
                                <?= $record['notes'] ? htmlspecialchars(substr($record['notes'], 0, 50)) . (strlen($record['notes']) > 50 ? '...' : '') : '--' ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Livestock Management System • Last updated: <?= date('Y-m-d H:i:s') ?>
    </div>

    <script>
        // Health Status Distribution Chart
        const healthStatusCtx = document.getElementById('healthStatusChart').getContext('2d');
        const healthStatusChart = new Chart(healthStatusCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode(array_column($status_distribution, 'health_status')) ?>,
                datasets: [{
                    data: <?= json_encode(array_column($status_distribution, 'count')) ?>,
                    backgroundColor: ['#48bb78', '#ed8936', '#f56565', '#4a6cf7', '#9f7aea'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
