<?php
/**
 * Database Configuration for ESP32 Livestock Management System
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'livestock_tracker');

// API Configuration
define('API_KEY', 'your_secure_api_key_here'); // Change this to a secure key
define('TIMEZONE', 'America/New_York'); // Change to your timezone

// Set timezone
date_default_timezone_set(TIMEZONE);

// CORS headers for ESP32 communication
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Database connection function
 */
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USERNAME,
            DB_PASSWORD,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        exit();
    }
}

/**
 * Validate API key (optional security measure)
 */
function validateApiKey() {
    $headers = getallheaders();
    $apiKey = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    
    // For now, we'll skip API key validation for ESP32 simplicity
    // Uncomment below lines to enable API key validation
    /*
    if ($apiKey !== 'Bearer ' . API_KEY) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        exit();
    }
    */
}

/**
 * Log API requests for debugging
 */
function logRequest($endpoint, $data = null) {
    $log = [
        'timestamp' => date('Y-m-d H:i:s'),
        'endpoint' => $endpoint,
        'method' => $_SERVER['REQUEST_METHOD'],
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'data' => $data
    ];
    
    error_log("API Request: " . json_encode($log));
}

/**
 * Send JSON response
 */
function sendResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Get JSON input from request body
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    return $data;
}

/**
 * Validate required fields in data
 */
function validateRequiredFields($data, $required_fields) {
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || $data[$field] === '') {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        sendResponse([
            'error' => 'Missing required fields',
            'missing_fields' => $missing_fields
        ], 400);
    }
}

/**
 * Update device status when it sends data
 */
function updateDeviceStatus($device_id, $pdo) {
    try {
        $stmt = $pdo->prepare("CALL UpdateDeviceStatus(?, ?)");
        $stmt->execute([$device_id, $_SERVER['REMOTE_ADDR']]);
    } catch (PDOException $e) {
        error_log("Failed to update device status: " . $e->getMessage());
    }
}
?>
