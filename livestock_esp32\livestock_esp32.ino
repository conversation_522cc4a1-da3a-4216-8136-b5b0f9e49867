#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <Wire.h>
#include <MPU9250.h>
#include <time.h>

// Pin definitions
#define DHT_PIN 4      // D4 (GPIO 4) - DHT11 data pin
#define DHT_TYPE DHT11
#define BUZZER_PIN 5   // D5 (GPIO 5) - Buzzer signal pin
#define SDA_PIN 2      // D2 (GPIO 2) - I2C SDA for MPU9250
#define SCL_PIN 18     // D18 (GPIO 18) - I2C SCL for MPU9250
#define LED_PIN 2      // Built-in LED (same as SDA, will blink when data sent)

// Sensor objects
DHT dht(DHT_PIN, DHT_TYPE);
MPU9250 mpu;

// WiFi credentials (replace with your network details)
const char* ssid = "Youngboy Never Broke Again";
const char* password = "NeverBroke!";\

// Server configuration (replace with your server details)
const char* serverURL = "http://192.168.0.180/livestock/api/sensor_data.php";
const char* alertURL = "http://192.168.0.180/livestock/api/alerts.php";

// Device configuration
String deviceID = "ESP32_001";
String location = "Barn A";  // Change this for diff erent locations

// Sensor thresholds
const float TEMP_HIGH_THRESHOLD = 30.0;
const float TEMP_LOW_THRESHOLD = 10.0;
const float HUMIDITY_HIGH_THRESHOLD = 80.0;
const float HUMIDITY_LOW_THRESHOLD = 30.0;
const float ACTIVITY_THRESHOLD = 2.0;  // G-force threshold for activity detection

// Timing variables
unsigned long lastSensorRead = 0;
unsigned long lastDataSend = 0;
const unsigned long SENSOR_INTERVAL = 5000;   // Read sensors every 5 seconds
const unsigned long SEND_INTERVAL = 30000;    // Send data every 30 seconds

// NTP time configuration
const char* ntpServer = "pool.ntp.org";
const long gmtOffset_sec = 0;
const int daylightOffset_sec = 3600;

// Data storage
struct SensorData {
  float temperature;
  float humidity;
  float activityLevel;
  bool motionDetected;
  unsigned long timestamp;
};

SensorData currentData;
bool alertActive = false;

void setup() {
  Serial.begin(115200);
  
  // Initialize pins
  pinMode(BUZZER_PIN, OUTPUT);
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(BUZZER_PIN, LOW);
  digitalWrite(LED_PIN, LOW);
  
  // Initialize sensors
  dht.begin();
  Wire.begin(SDA_PIN, SCL_PIN);  // Initialize I2C with custom pins

  // Initialize MPU9250
  if (!mpu.setup(0x68)) {
    Serial.println("MPU9250 connection failed");
    blinkError();
  } else {
    Serial.println("MPU9250 initialized successfully");
  }
  
  // Connect to WiFi
  connectToWiFi();

  // Initialize time
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);
  waitForTimeSync();

  Serial.println("ESP32 Livestock Management System Started");
  Serial.println("Device ID: " + deviceID);
  Serial.println("Location: " + location);

  // Initial sensor calibration
  calibrateSensors();
}

void loop() {
  unsigned long currentTime = millis();
  
  // Read sensors at specified interval
  if (currentTime - lastSensorRead >= SENSOR_INTERVAL) {
    readSensors();
    checkAlerts();
    lastSensorRead = currentTime;
  }
  
  // Send data to server at specified interval
  if (currentTime - lastDataSend >= SEND_INTERVAL) {
    if (WiFi.status() == WL_CONNECTED) {
      sendSensorData();
      lastDataSend = currentTime;
    } else {
      Serial.println("WiFi disconnected, attempting reconnection...");
      connectToWiFi();
    }
  }
  
  // Handle alerts
  if (alertActive) {
    handleAlert();
  }
  
  delay(100);  // Small delay to prevent watchdog issues
}

void connectToWiFi() {
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("WiFi connected successfully");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
    digitalWrite(LED_PIN, HIGH);  // LED on when connected
  } else {
    Serial.println();
    Serial.println("WiFi connection failed");
    blinkError();
  }
}

void readSensors() {
  // Read DHT11 sensor
  currentData.temperature = dht.readTemperature();
  currentData.humidity = dht.readHumidity();
  
  // Check for DHT11 read errors
  if (isnan(currentData.temperature) || isnan(currentData.humidity)) {
    Serial.println("Failed to read from DHT sensor!");
    currentData.temperature = -999;
    currentData.humidity = -999;
  }
  
  // Read MPU9250 accelerometer
  if (mpu.update()) {
    float ax = mpu.getAccX();
    float ay = mpu.getAccY();
    float az = mpu.getAccZ();
    
    // Calculate total acceleration magnitude
    currentData.activityLevel = sqrt(ax*ax + ay*ay + az*az);
    currentData.motionDetected = (currentData.activityLevel > ACTIVITY_THRESHOLD);
  } else {
    Serial.println("Failed to read from MPU9250!");
    currentData.activityLevel = 0;
    currentData.motionDetected = false;
  }
  
  currentData.timestamp = millis();
  
  // Print sensor readings
  Serial.println("=== Sensor Readings ===");
  Serial.println("Temperature: " + String(currentData.temperature) + "°C");
  Serial.println("Humidity: " + String(currentData.humidity) + "%");
  Serial.println("Activity Level: " + String(currentData.activityLevel) + "g");
  Serial.println("Motion Detected: " + String(currentData.motionDetected ? "Yes" : "No"));
  Serial.println("=======================");
}

void checkAlerts() {
  String alertMessage = "";
  String alertType = "";
  
  // Check temperature alerts
  if (currentData.temperature > TEMP_HIGH_THRESHOLD) {
    alertMessage = "High temperature detected: " + String(currentData.temperature) + "°C at " + location;
    alertType = "High Temperature";
    alertActive = true;
  } else if (currentData.temperature < TEMP_LOW_THRESHOLD && currentData.temperature > -900) {
    alertMessage = "Low temperature detected: " + String(currentData.temperature) + "°C at " + location;
    alertType = "Low Temperature";
    alertActive = true;
  }
  
  // Check humidity alerts
  if (currentData.humidity > HUMIDITY_HIGH_THRESHOLD) {
    alertMessage = "High humidity detected: " + String(currentData.humidity) + "% at " + location;
    alertType = "High Humidity";
    alertActive = true;
  } else if (currentData.humidity < HUMIDITY_LOW_THRESHOLD && currentData.humidity > -900) {
    alertMessage = "Low humidity detected: " + String(currentData.humidity) + "% at " + location;
    alertType = "Low Humidity";
    alertActive = true;
  }
  
  // Check for very low activity (possible health issue)
  if (currentData.activityLevel < 0.5 && currentData.activityLevel > 0) {
    alertMessage = "Very low activity detected: " + String(currentData.activityLevel) + "g at " + location;
    alertType = "Low Activity";
    alertActive = true;
  }
  
  // Send alert to server if needed
  if (alertActive && alertMessage.length() > 0) {
    sendAlert(alertType, alertMessage);
  }
}

void handleAlert() {
  // Sound buzzer pattern for alerts
  for (int i = 0; i < 3; i++) {
    digitalWrite(BUZZER_PIN, HIGH);
    delay(200);
    digitalWrite(BUZZER_PIN, LOW);
    delay(200);
  }
  
  // Reset alert after handling
  alertActive = false;
  delay(5000);  // Wait 5 seconds before next alert can trigger
}

void calibrateSensors() {
  Serial.println("Calibrating sensors...");
  
  // MPU9250 calibration
  Serial.println("Calibrating MPU9250 - keep device still");
  delay(1000);
  mpu.calibrateAccelGyro();
  
  Serial.println("Sensor calibration complete");
}

void blinkError() {
  for (int i = 0; i < 10; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(100);
    digitalWrite(LED_PIN, LOW);
    delay(100);
  }
}

void sendSensorData() {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(serverURL);
    http.addHeader("Content-Type", "application/json");

    // Create JSON payload
    DynamicJsonDocument doc(1024);
    doc["device_id"] = deviceID;
    doc["location"] = location;
    doc["temperature"] = currentData.temperature;
    doc["humidity"] = currentData.humidity;
    doc["activity_level"] = currentData.activityLevel;
    doc["motion_detected"] = currentData.motionDetected ? 1 : 0;

    // Get current timestamp
    time_t now;
    time(&now);
    doc["timestamp"] = now;

    String jsonString;
    serializeJson(doc, jsonString);

    Serial.println("Sending data: " + jsonString);

    int httpResponseCode = http.POST(jsonString);

    if (httpResponseCode > 0) {
      String response = http.getString();
      Serial.println("HTTP Response: " + String(httpResponseCode));
      Serial.println("Response: " + response);
    } else {
      Serial.println("Error sending data: " + String(httpResponseCode));
    }

    http.end();
  }
}

void sendAlert(String alertType, String alertMessage) {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(alertURL);
    http.addHeader("Content-Type", "application/json");

    // Create JSON payload for alert
    DynamicJsonDocument doc(1024);
    doc["device_id"] = deviceID;
    doc["location"] = location;
    doc["alert_type"] = alertType;
    doc["alert_message"] = alertMessage;

    // Get current timestamp
    time_t now;
    time(&now);
    doc["timestamp"] = now;
    doc["resolved"] = 0;

    String jsonString;
    serializeJson(doc, jsonString);

    Serial.println("Sending alert: " + jsonString);

    int httpResponseCode = http.POST(jsonString);

    if (httpResponseCode > 0) {
      String response = http.getString();
      Serial.println("Alert sent successfully: " + String(httpResponseCode));
    } else {
      Serial.println("Error sending alert: " + String(httpResponseCode));
    }

    http.end();
  }
}

// Function to get formatted timestamp string
String getTimestamp() {
  time_t now;
  time(&now);
  struct tm timeinfo;
  localtime_r(&now, &timeinfo);

  char timeString[64];
  strftime(timeString, sizeof(timeString), "%Y-%m-%d %H:%M:%S", &timeinfo);
  return String(timeString);
}

// Function to wait for NTP time synchronization
void waitForTimeSync() {
  Serial.print("Waiting for NTP time sync: ");
  time_t nowSecs = time(nullptr);
  while (nowSecs < 8 * 3600 * 2) {
    delay(500);
    Serial.print(".");
    yield();
    nowSecs = time(nullptr);
  }
  Serial.println();
  Serial.println("Time synchronized: " + getTimestamp());
}
