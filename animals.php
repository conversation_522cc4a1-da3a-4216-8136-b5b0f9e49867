<?php
/**
 * Animals Management Page
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

// Get animals data with latest health records
$animals_stmt = $pdo->query("
    SELECT 
        a.*,
        hr.weight,
        hr.temperature,
        hr.health_status,
        hr.record_date,
        hr.notes as health_notes
    FROM animals a
    LEFT JOIN (
        SELECT 
            animal_id,
            weight,
            temperature,
            health_status,
            record_date,
            notes,
            ROW_NUMBER() OVER (PARTITION BY animal_id ORDER BY record_date DESC) as rn
        FROM health_records
    ) hr ON a.animal_id = hr.animal_id AND hr.rn = 1
    WHERE a.status = 'Active'
    ORDER BY a.animal_id
");
$animals = $animals_stmt->fetchAll();

// Get species distribution
$species_stmt = $pdo->query("
    SELECT species, COUNT(*) as count 
    FROM animals 
    WHERE status = 'Active' 
    GROUP BY species
");
$species_data = $species_stmt->fetchAll();

// Get recent births (last 30 days)
$births_stmt = $pdo->query("
    SELECT COUNT(*) as recent_births 
    FROM animals 
    WHERE birth_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND status = 'Active'
");
$recent_births = $births_stmt->fetch()['recent_births'];

// Get health status distribution
$health_status_stmt = $pdo->query("
    SELECT 
        COALESCE(hr.health_status, 'Unknown') as status,
        COUNT(*) as count
    FROM animals a
    LEFT JOIN (
        SELECT 
            animal_id,
            health_status,
            ROW_NUMBER() OVER (PARTITION BY animal_id ORDER BY record_date DESC) as rn
        FROM health_records
    ) hr ON a.animal_id = hr.animal_id AND hr.rn = 1
    WHERE a.status = 'Active'
    GROUP BY hr.health_status
");
$health_status_data = $health_status_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livestock Manager - Animals</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Page Layout */
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a6cf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3c5aa6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Metric Cards */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.blue { background: #4a6cf7; }
        .metric-icon.green { background: #48bb78; }
        .metric-icon.purple { background: #9f7aea; }
        .metric-icon.orange { background: #ed8936; }

        .metric-content h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #2d3748;
        }

        .metric-content p {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .data-table tbody tr:hover {
            background-color: #f7fafc;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-healthy {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-monitoring {
            background-color: #fbb6ce;
            color: #97266d;
        }

        .status-sick {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-unknown {
            background-color: #e2e8f0;
            color: #4a5568;
        }

        /* Animal Photo */
        .animal-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 0.5rem;
        }

        .no-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            color: #a0aec0;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: #718096;
            font-size: 0.875rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link active">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">Animals</h1>
            <p class="page-subtitle">Manage and monitor your livestock</p>
        </div>
        <a href="manage_animals.php" class="btn btn-primary">
            <i class="fas fa-cog"></i> Manage Animals
        </a>
    </div>

    <div class="container">
        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon blue">
                    <i class="fas fa-paw"></i>
                </div>
                <div class="metric-content">
                    <h3><?= count($animals) ?></h3>
                    <p>Total Animals</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon green">
                    <i class="fas fa-baby"></i>
                </div>
                <div class="metric-content">
                    <h3><?= $recent_births ?></h3>
                    <p>Recent Births (30 days)</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon purple">
                    <i class="fas fa-dna"></i>
                </div>
                <div class="metric-content">
                    <h3><?= count($species_data) ?></h3>
                    <p>Species Types</p>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-icon orange">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="metric-content">
                    <h3><?= count(array_filter($animals, function($a) { return $a['health_status'] === 'Healthy'; })) ?></h3>
                    <p>Healthy Animals</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Species Distribution Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Species Distribution</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="speciesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Health Status Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Health Status Overview</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="healthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animals Table -->
        <div class="card full-width">
            <div class="card-header">
                <h3 class="card-title">Animal Records</h3>
            </div>
            <div class="card-body">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Animal</th>
                            <th>Tag Number</th>
                            <th>Species</th>
                            <th>Breed</th>
                            <th>Age</th>
                            <th>Gender</th>
                            <th>Weight</th>
                            <th>Health Status</th>
                            <th>Last Check</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($animals as $animal): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <?php if ($animal['photo']): ?>
                                        <img src="uploads/<?= htmlspecialchars($animal['photo']) ?>" alt="Animal Photo" class="animal-photo">
                                    <?php else: ?>
                                        <div class="no-photo">
                                            <i class="fas fa-paw"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <strong>Animal #<?= $animal['animal_id'] ?></strong>
                                    </div>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($animal['tag_number']) ?></td>
                            <td><?= htmlspecialchars($animal['species']) ?></td>
                            <td><?= htmlspecialchars($animal['breed']) ?></td>
                            <td>
                                <?php 
                                $birth_date = new DateTime($animal['birth_date']);
                                $now = new DateTime();
                                $age = $birth_date->diff($now);
                                echo $age->y . 'y ' . $age->m . 'm';
                                ?>
                            </td>
                            <td><?= htmlspecialchars($animal['gender']) ?></td>
                            <td>
                                <?= $animal['weight'] ? number_format($animal['weight'], 1) . ' kg' : '--' ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?= strtolower($animal['health_status'] ?: 'unknown') ?>">
                                    <?= htmlspecialchars($animal['health_status'] ?: 'Unknown') ?>
                                </span>
                            </td>
                            <td>
                                <?= $animal['record_date'] ? date('M j, Y', strtotime($animal['record_date'])) : 'Never' ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Livestock Management System • Last updated: <?= date('Y-m-d H:i:s') ?>
    </div>

    <script>
        // Species Distribution Chart
        const speciesCtx = document.getElementById('speciesChart').getContext('2d');
        const speciesChart = new Chart(speciesCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode(array_column($species_data, 'species')) ?>,
                datasets: [{
                    data: <?= json_encode(array_column($species_data, 'count')) ?>,
                    backgroundColor: ['#4a6cf7', '#48bb78', '#9f7aea', '#ed8936', '#f56565'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Health Status Chart
        const healthCtx = document.getElementById('healthChart').getContext('2d');
        const healthChart = new Chart(healthCtx, {
            type: 'bar',
            data: {
                labels: <?= json_encode(array_column($health_status_data, 'status')) ?>,
                datasets: [{
                    data: <?= json_encode(array_column($health_status_data, 'count')) ?>,
                    backgroundColor: ['#48bb78', '#ed8936', '#f56565', '#a0aec0'],
                    borderWidth: 0,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
