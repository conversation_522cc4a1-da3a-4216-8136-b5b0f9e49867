-- S<PERSON> Script to Remove Weight from Livestock Management System
-- Run this in phpMyAdmin or MySQL command line

USE livestock_tracker;

-- Step 1: Remove weight column from health_records table
ALTER TABLE health_records DROP COLUMN weight;

-- Step 2: Update any views that might reference weight (if they exist)
-- Note: This will recreate the view without weight references

-- Step 3: Clean up any weight-related data in notes (optional)
-- This removes weight references from existing notes
UPDATE health_records 
SET notes = TRIM(REGEXP_REPLACE(notes, 'Weight: [0-9]+\\.?[0-9]*kg[,\\.]?\\s*', ''))
WHERE notes LIKE '%Weight:%kg%';

-- Step 4: Verify the changes
DESCRIBE health_records;

-- Expected result: weight column should no longer exist
-- Remaining columns should be:
-- record_id, animal_id, record_date, temperature, health_status, treatment, notes, veterinarian

SELECT 'Weight column removal completed successfully!' as status;
