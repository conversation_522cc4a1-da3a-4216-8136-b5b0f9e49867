<?php
/**
 * ESP32 Devices API Endpoint
 * Handles device management and status
 */

require_once 'config.php';

// Log the request
logRequest('devices', $_POST);

// Validate API key (optional)
validateApiKey();

// Get database connection
$pdo = getDBConnection();

// Handle different HTTP methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleDeviceGet($pdo);
        break;
    case 'POST':
        handleDevicePost($pdo);
        break;
    case 'PUT':
        handleDevicePut($pdo);
        break;
    default:
        sendResponse(['error' => 'Method not allowed'], 405);
}

/**
 * Handle GET request - retrieve device information
 */
function handleDeviceGet($pdo) {
    $device_id = $_GET['device_id'] ?? null;
    $status = $_GET['status'] ?? null;
    $location = $_GET['location'] ?? null;
    
    try {
        if ($device_id) {
            // Get specific device with latest sensor data
            $stmt = $pdo->prepare("
                SELECT 
                    ed.*,
                    lsr.temperature,
                    lsr.humidity,
                    lsr.activity_level,
                    lsr.motion_detected,
                    lsr.timestamp as last_reading
                FROM esp32_devices ed
                LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
                WHERE ed.device_id = ?
            ");
            
            $stmt->execute([$device_id]);
            $device = $stmt->fetch();
            
            if ($device) {
                // Get recent alerts for this device
                $alert_stmt = $pdo->prepare("
                    SELECT * FROM device_alerts 
                    WHERE device_id = ? AND resolved = 0 
                    ORDER BY timestamp DESC LIMIT 5
                ");
                $alert_stmt->execute([$device_id]);
                $device['recent_alerts'] = $alert_stmt->fetchAll();
                
                sendResponse([
                    'success' => true,
                    'device' => $device
                ]);
            } else {
                sendResponse(['error' => 'Device not found'], 404);
            }
        } else {
            // Get all devices
            $sql = "
                SELECT 
                    ed.*,
                    lsr.temperature,
                    lsr.humidity,
                    lsr.activity_level,
                    lsr.motion_detected,
                    lsr.timestamp as last_reading,
                    COUNT(da.id) as active_alerts
                FROM esp32_devices ed
                LEFT JOIN latest_sensor_readings lsr ON ed.device_id = lsr.device_id
                LEFT JOIN device_alerts da ON ed.device_id = da.device_id AND da.resolved = 0
            ";
            
            $params = [];
            $where_conditions = [];
            
            if ($status) {
                $where_conditions[] = "ed.status = ?";
                $params[] = $status;
            }
            
            if ($location) {
                $where_conditions[] = "ed.location = ?";
                $params[] = $location;
            }
            
            if (!empty($where_conditions)) {
                $sql .= " WHERE " . implode(" AND ", $where_conditions);
            }
            
            $sql .= " GROUP BY ed.device_id ORDER BY ed.device_id";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $devices = $stmt->fetchAll();
            
            // Get summary statistics
            $stats_stmt = $pdo->prepare("
                SELECT 
                    COUNT(*) as total_devices,
                    SUM(CASE WHEN status = 'Online' THEN 1 ELSE 0 END) as online_devices,
                    SUM(CASE WHEN status = 'Offline' THEN 1 ELSE 0 END) as offline_devices,
                    SUM(CASE WHEN status = 'Error' THEN 1 ELSE 0 END) as error_devices
                FROM esp32_devices
            ");
            $stats_stmt->execute();
            $stats = $stats_stmt->fetch();
            
            sendResponse([
                'success' => true,
                'devices' => $devices,
                'statistics' => $stats,
                'count' => count($devices)
            ]);
        }
        
    } catch (PDOException $e) {
        error_log("Database error in devices.php GET: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Handle POST request - register new device
 */
function handleDevicePost($pdo) {
    $data = getJsonInput();
    
    // Validate required fields
    $required_fields = ['device_id', 'device_name', 'location'];
    validateRequiredFields($data, $required_fields);
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO esp32_devices 
            (device_id, device_name, location, status, ip_address, last_seen) 
            VALUES (?, ?, ?, 'Offline', ?, NOW())
            ON DUPLICATE KEY UPDATE
            device_name = VALUES(device_name),
            location = VALUES(location),
            ip_address = VALUES(ip_address),
            last_seen = NOW()
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        
        $stmt->execute([
            $data['device_id'],
            $data['device_name'],
            $data['location'],
            $ip_address
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Device registered successfully',
            'device_id' => $data['device_id']
        ]);
        
    } catch (PDOException $e) {
        error_log("Database error in devices.php POST: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}

/**
 * Handle PUT request - update device information
 */
function handleDevicePut($pdo) {
    $data = getJsonInput();
    
    // Validate required fields
    $required_fields = ['device_id'];
    validateRequiredFields($data, $required_fields);
    
    try {
        $update_fields = [];
        $params = [];
        
        if (isset($data['device_name'])) {
            $update_fields[] = "device_name = ?";
            $params[] = $data['device_name'];
        }
        
        if (isset($data['location'])) {
            $update_fields[] = "location = ?";
            $params[] = $data['location'];
        }
        
        if (isset($data['status'])) {
            $update_fields[] = "status = ?";
            $params[] = $data['status'];
        }
        
        if (isset($data['firmware_version'])) {
            $update_fields[] = "firmware_version = ?";
            $params[] = $data['firmware_version'];
        }
        
        if (empty($update_fields)) {
            sendResponse(['error' => 'No fields to update'], 400);
        }
        
        $update_fields[] = "updated_at = NOW()";
        $params[] = $data['device_id'];
        
        $sql = "UPDATE esp32_devices SET " . implode(", ", $update_fields) . " WHERE device_id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            sendResponse([
                'success' => true,
                'message' => 'Device updated successfully',
                'device_id' => $data['device_id']
            ]);
        } else {
            sendResponse(['error' => 'Device not found'], 404);
        }
        
    } catch (PDOException $e) {
        error_log("Database error in devices.php PUT: " . $e->getMessage());
        sendResponse(['error' => 'Database error occurred'], 500);
    }
}
?>
