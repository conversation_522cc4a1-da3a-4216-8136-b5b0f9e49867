<?php
/**
 * Cleanup Invalid Sensor Data
 * Removes or fixes invalid sensor readings (-999 values)
 */

require_once 'api/config.php';

// Get database connection
$pdo = getDBConnection();

echo "<!DOCTYPE html>";
echo "<html><head><title>Sensor Data Cleanup</title>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    h1 { color: #333; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
</style>";
echo "</head><body>";

echo "<h1>🧹 Sensor Data Cleanup Tool</h1>";

// Check for invalid data
$invalid_count_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_invalid,
        COUNT(CASE WHEN temperature = -999 THEN 1 END) as invalid_temp,
        COUNT(CASE WHEN humidity = -999 THEN 1 END) as invalid_humidity
    FROM realtime_sensor_data 
    WHERE temperature = -999 OR humidity = -999
");
$invalid_count = $invalid_count_stmt->fetch();

echo "<div class='result info'>";
echo "<h3>📊 Invalid Data Summary</h3>";
echo "<p><strong>Total invalid readings:</strong> " . $invalid_count['total_invalid'] . "</p>";
echo "<p><strong>Invalid temperature readings:</strong> " . $invalid_count['invalid_temp'] . "</p>";
echo "<p><strong>Invalid humidity readings:</strong> " . $invalid_count['invalid_humidity'] . "</p>";
echo "</div>";

// If cleanup is requested
if (isset($_GET['action']) && $_GET['action'] === 'cleanup') {
    try {
        // Delete invalid readings
        $delete_stmt = $pdo->prepare("DELETE FROM realtime_sensor_data WHERE temperature = -999 OR humidity = -999");
        $delete_stmt->execute();
        $deleted_count = $delete_stmt->rowCount();
        
        echo "<div class='result success'>";
        echo "<h3>✅ Cleanup Complete</h3>";
        echo "<p>Deleted <strong>$deleted_count</strong> invalid sensor readings.</p>";
        echo "</div>";
        
        // Update device status for devices with no recent valid data
        $update_devices_stmt = $pdo->query("
            UPDATE esp32_devices 
            SET status = 'Offline' 
            WHERE device_id NOT IN (
                SELECT DISTINCT device_id 
                FROM realtime_sensor_data 
                WHERE timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND temperature != -999 
                AND humidity != -999
            )
        ");
        $updated_devices = $update_devices_stmt->rowCount();
        
        echo "<div class='result info'>";
        echo "<h3>📱 Device Status Updated</h3>";
        echo "<p>Updated status for <strong>$updated_devices</strong> devices with no recent valid data.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='result warning'>";
        echo "<h3>❌ Cleanup Failed</h3>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

// Show recent invalid readings
echo "<h2>🔍 Recent Invalid Readings</h2>";
$recent_invalid_stmt = $pdo->query("
    SELECT device_id, temperature, humidity, timestamp 
    FROM realtime_sensor_data 
    WHERE temperature = -999 OR humidity = -999 
    ORDER BY timestamp DESC 
    LIMIT 20
");
$recent_invalid = $recent_invalid_stmt->fetchAll();

if (empty($recent_invalid)) {
    echo "<div class='result success'>";
    echo "<p>✅ No recent invalid readings found!</p>";
    echo "</div>";
} else {
    echo "<table style='width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden;'>";
    echo "<thead style='background: #f8f9fa;'>";
    echo "<tr><th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Device ID</th>";
    echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Temperature</th>";
    echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Humidity</th>";
    echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Timestamp</th></tr>";
    echo "</thead><tbody>";
    
    foreach ($recent_invalid as $reading) {
        echo "<tr>";
        echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'>" . htmlspecialchars($reading['device_id']) . "</td>";
        echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6; color: " . ($reading['temperature'] == -999 ? '#dc3545' : '#28a745') . ";'>" . $reading['temperature'] . "°C</td>";
        echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6; color: " . ($reading['humidity'] == -999 ? '#dc3545' : '#28a745') . ";'>" . $reading['humidity'] . "%</td>";
        echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'>" . date('Y-m-d H:i:s', strtotime($reading['timestamp'])) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
}

// Show device summary
echo "<h2>📱 Device Summary</h2>";
$device_summary_stmt = $pdo->query("
    SELECT 
        ed.device_id,
        ed.device_name,
        ed.status,
        COUNT(rsd.id) as total_readings,
        COUNT(CASE WHEN rsd.temperature = -999 OR rsd.humidity = -999 THEN 1 END) as invalid_readings,
        MAX(rsd.timestamp) as last_reading
    FROM esp32_devices ed
    LEFT JOIN realtime_sensor_data rsd ON ed.device_id = rsd.device_id
    GROUP BY ed.device_id, ed.device_name, ed.status
    ORDER BY ed.device_id
");
$device_summary = $device_summary_stmt->fetchAll();

echo "<table style='width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; margin-top: 1rem;'>";
echo "<thead style='background: #f8f9fa;'>";
echo "<tr><th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Device ID</th>";
echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Name</th>";
echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Status</th>";
echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Total Readings</th>";
echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Invalid Readings</th>";
echo "<th style='padding: 10px; border-bottom: 1px solid #dee2e6;'>Last Reading</th></tr>";
echo "</thead><tbody>";

foreach ($device_summary as $device) {
    $error_rate = $device['total_readings'] > 0 ? ($device['invalid_readings'] / $device['total_readings']) * 100 : 0;
    $status_color = $device['status'] === 'Online' ? '#28a745' : '#dc3545';
    
    echo "<tr>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'><strong>" . htmlspecialchars($device['device_id']) . "</strong></td>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'>" . htmlspecialchars($device['device_name']) . "</td>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6; color: $status_color;'>" . $device['status'] . "</td>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'>" . $device['total_readings'] . "</td>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6; color: " . ($device['invalid_readings'] > 0 ? '#dc3545' : '#28a745') . ";'>" . $device['invalid_readings'] . " (" . number_format($error_rate, 1) . "%)</td>";
    echo "<td style='padding: 10px; border-bottom: 1px solid #dee2e6;'>" . ($device['last_reading'] ? date('M j, H:i', strtotime($device['last_reading'])) : 'Never') . "</td>";
    echo "</tr>";
}
echo "</tbody></table>";

// Action buttons
echo "<div style='margin-top: 2rem; text-align: center;'>";
if ($invalid_count['total_invalid'] > 0) {
    echo "<a href='?action=cleanup' class='btn' style='background: #dc3545;' onclick='return confirm(\"Are you sure you want to delete all invalid sensor readings?\")'>🗑️ Clean Up Invalid Data</a>";
}
echo "<a href='esp32_diagnostics.php' class='btn' style='background: #17a2b8;'>🔧 View Diagnostics</a>";
echo "<a href='iot_data.php' class='btn'>📊 Back to IoT Data</a>";
echo "</div>";

// ESP32 Code Recommendations
echo "<div style='margin-top: 2rem; padding: 1.5rem; background: #e7f3ff; border-radius: 8px; border-left: 4px solid #007bff;'>";
echo "<h3 style='color: #004085;'>💡 ESP32 Code Recommendations</h3>";
echo "<p style='color: #004085;'>To fix the -999 sensor readings, update your ESP32 code:</p>";
echo "<pre style='background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto;'>";
echo "// Add sensor validation before sending data
float temp = dht.readTemperature();
float humidity = dht.readHumidity();

// Only send valid readings
if (!isnan(temp) && !isnan(humidity) && temp > -50 && temp < 80 && humidity >= 0 && humidity <= 100) {
    // Send data to server
    sendSensorData(temp, humidity);
} else {
    Serial.println(\"Invalid sensor reading, skipping...\");
    // Optional: Try reading again after delay
    delay(2000);
}";
echo "</pre>";
echo "</div>";

echo "</body></html>";
?>
