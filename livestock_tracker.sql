-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 14, 2025 at 07:42 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `livestock_tracker`
--

-- --------------------------------------------------------

--
-- Table structure for table `alerts`
--

CREATE TABLE `alerts` (
  `id` int(11) NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `alert_type` varchar(50) DEFAULT NULL,
  `alert_message` text DEFAULT NULL,
  `resolved` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `alerts`
--

INSERT INTO `alerts` (`id`, `timestamp`, `alert_type`, `alert_message`, `resolved`) VALUES
(1, '2024-07-12 14:30:00', 'High Temperature', 'High temperature detected: 32.8°C at Pasture 1', 1),
(2, '2024-07-12 10:15:00', 'Health Alert', 'Animal SHEEP003 recovered from illness - status changed to Healthy', 1),
(3, '2024-07-11 16:45:00', 'Low Activity', 'Very low activity detected: 8.2% at Pasture 1', 1),
(4, '2024-07-11 09:20:00', 'Health Alert', 'Animal SHEEP003 showing signs of illness - temperature 40.1°C', 1),
(5, '2024-07-10 18:30:00', 'Feeding Alert', 'Feeding schedule reminder for Barn A animals', 1),
(6, '2024-07-12 22:15:00', 'System Alert', 'All systems operating normally', 0),
(7, '2024-07-12 20:45:00', 'Environmental Alert', 'Optimal conditions maintained across all locations', 0);

-- --------------------------------------------------------

--
-- Table structure for table `animals`
--

CREATE TABLE `animals` (
  `animal_id` int(11) NOT NULL,
  `tag_number` varchar(50) DEFAULT NULL,
  `species` varchar(50) DEFAULT NULL,
  `breed` varchar(50) DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `gender` enum('Male','Female') DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `status` enum('Active','Sold','Deceased') DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `photo_uploaded_at` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `animals`
--

INSERT INTO `animals` (`animal_id`, `tag_number`, `species`, `breed`, `birth_date`, `gender`, `parent_id`, `status`, `photo`, `photo_uploaded_at`, `notes`) VALUES
(1, 'TEST-1752375635700', 'Cattle', 'Holstein', '2023-01-10', 'Female', NULL, 'Active', 'animal_1_1752374713.jpg', '2025-07-13 02:45:13', 'Test save from debug page'),
(2, 'COW002-TEST-9000', 'Cattle', 'Angus', '2021-11-20', 'Male', NULL, 'Active', 'animal_2_1752374786.jpg', '2025-07-13 02:46:26', 'Updated via duplicate tag test'),
(3, 'COW016', 'Cattle', 'Holstein', '2022-01-10', 'Female', NULL, 'Active', NULL, NULL, ''),
(4, 'COW004', 'Cattle', 'Jersey', '2021-08-05', 'Male', NULL, 'Active', NULL, NULL, ''),
(5, 'COW005', 'Cattle', 'Jersey', '2022-05-12', 'Male', NULL, 'Active', NULL, NULL, ''),
(6, 'SHEEP001', 'Sheep', 'Merino', '2023-02-14', 'Female', NULL, 'Active', NULL, NULL, NULL),
(7, 'SHEEP002', 'Sheep', 'Suffolk', '2023-01-20', 'Male', NULL, 'Active', NULL, NULL, NULL),
(8, 'SHEEP003', 'Sheep', 'Merino', '2022-12-08', 'Female', NULL, 'Active', NULL, NULL, NULL),
(9, 'PIG001', 'Pig', 'Yorkshire', '2023-06-15', 'Female', NULL, 'Active', NULL, NULL, NULL),
(10, 'PIG002', 'Pig', 'Duroc', '2023-05-20', 'Female', NULL, 'Active', NULL, NULL, ''),
(11, 'SHEEP004', 'Sheep', 'Merino', '2023-12-10', 'Male', NULL, 'Active', NULL, NULL, NULL),
(12, 'cow007', 'Cattle', 'Jersey', '2025-07-22', 'Male', 1, 'Active', NULL, NULL, NULL),
(13, 'COW006', 'Cattle', 'Holstein', '2024-01-15', 'Female', NULL, 'Active', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `feeding_records`
--

CREATE TABLE `feeding_records` (
  `record_id` int(11) NOT NULL,
  `animal_id` int(11) DEFAULT NULL,
  `feed_type` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `feeding_time` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `feeding_records`
--

INSERT INTO `feeding_records` (`record_id`, `animal_id`, `feed_type`, `amount`, `feeding_time`) VALUES
(1, 1, 'Hay', 15.50, '2024-07-12 07:00:00'),
(2, 1, 'Grain', 3.20, '2024-07-12 17:00:00'),
(3, 2, 'Hay', 18.00, '2024-07-12 07:00:00'),
(4, 2, 'Grain', 4.00, '2024-07-12 17:00:00'),
(5, 3, 'Hay', 14.00, '2024-07-12 07:00:00'),
(6, 3, 'Grain', 2.80, '2024-07-12 17:00:00'),
(7, 4, 'Hay', 12.50, '2024-07-12 07:00:00'),
(8, 4, 'Grain', 2.50, '2024-07-12 17:00:00'),
(9, 5, 'Hay', 16.50, '2024-07-12 07:00:00'),
(10, 5, 'Grain', 3.50, '2024-07-12 17:00:00'),
(11, 6, 'Grass', 2.50, '2024-07-12 08:00:00'),
(12, 6, 'Pellets', 0.80, '2024-07-12 18:00:00'),
(13, 7, 'Grass', 3.00, '2024-07-12 08:00:00'),
(14, 7, 'Pellets', 1.00, '2024-07-12 18:00:00'),
(15, 8, 'Grass', 2.20, '2024-07-12 08:00:00'),
(16, 8, 'Pellets', 0.70, '2024-07-12 18:00:00'),
(17, 9, 'Corn', 4.50, '2024-07-12 09:00:00'),
(18, 9, 'Soy', 1.50, '2024-07-12 19:00:00'),
(19, 10, 'Corn', 5.00, '2024-07-12 09:00:00'),
(20, 10, 'Soy', 1.80, '2024-07-12 19:00:00'),
(21, 1, 'Hay', 15.00, '2024-06-15 07:00:00'),
(22, 1, 'Grain', 3.00, '2024-06-15 17:00:00'),
(23, 2, 'Hay', 17.50, '2024-06-15 07:00:00'),
(24, 2, 'Grain', 3.80, '2024-06-15 17:00:00'),
(25, 3, 'Hay', 13.50, '2024-06-15 07:00:00'),
(26, 3, 'Grain', 2.60, '2024-06-15 17:00:00'),
(27, 4, 'Hay', 12.00, '2024-06-15 07:00:00'),
(28, 4, 'Grain', 2.30, '2024-06-15 17:00:00'),
(29, 5, 'Hay', 16.00, '2024-06-15 07:00:00'),
(30, 5, 'Grain', 3.30, '2024-06-15 17:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `health_records`
--

CREATE TABLE `health_records` (
  `record_id` int(11) NOT NULL,
  `animal_id` int(11) DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `temperature` decimal(5,2) DEFAULT NULL,
  `health_status` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `health_records`
--

INSERT INTO `health_records` (`record_id`, `animal_id`, `record_date`, `weight`, `temperature`, `health_status`, `notes`) VALUES
(52, 1, '2025-05-17', 402.20, 40.90, 'Healthy', 'Animal appears alert and active. Weight: 402.2kg, Temperature: 40.9°C'),
(53, 1, '2025-05-23', 543.40, 40.70, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 543.4kg, Temperature: 40.7°C'),
(54, 1, '2025-05-27', 571.60, 39.40, 'Monitoring', 'Close observation recommended. Weight: 571.6kg, Temperature: 39.4°C'),
(55, 1, '2025-05-19', 482.50, 40.40, 'Good', 'Routine checkup - generally healthy. Weight: 482.5kg, Temperature: 40.4°C'),
(56, 1, '2025-05-14', 491.90, 39.40, 'Monitoring', 'Preventive monitoring initiated. Weight: 491.9kg, Temperature: 39.4°C'),
(57, 2, '2025-07-05', 440.60, 39.60, 'Sick', 'Respiratory symptoms - antibiotic prescribed. Weight: 440.6kg, Temperature: 39.6°C'),
(58, 2, '2025-05-16', 535.60, 37.30, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 535.6kg, Temperature: 37.3°C'),
(59, 2, '2025-07-08', 586.80, 37.30, 'Healthy', 'Weekly monitoring - optimal health. Weight: 586.8kg, Temperature: 37.3°C'),
(60, 2, '2025-07-03', 566.30, 40.40, 'Sick', 'Treatment protocol started. Weight: 566.3kg, Temperature: 40.4°C'),
(61, 2, '2025-05-31', 423.50, 37.50, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 423.5kg, Temperature: 37.5°C'),
(62, 3, '2025-04-14', 550.90, 37.80, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 550.9kg, Temperature: 37.8°C'),
(63, 3, '2025-06-11', 490.00, 39.30, 'Monitoring', 'Behavioral changes observed, requires attention. Weight: 490kg, Temperature: 39.3°C'),
(64, 3, '2025-04-23', 424.70, 40.70, 'Critical', 'Life-threatening situation - urgent treatment. Weight: 424.7kg, Temperature: 40.7°C'),
(65, 3, '2025-05-17', 469.10, 38.50, 'Monitoring', 'Close observation recommended. Weight: 469.1kg, Temperature: 38.5°C'),
(66, 3, '2025-05-27', 488.30, 39.60, 'Sick', 'Infection suspected - isolation recommended. Weight: 488.3kg, Temperature: 39.6°C'),
(67, 3, '2025-06-15', 540.50, 38.90, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 540.5kg, Temperature: 38.9°C'),
(68, 4, '2025-06-02', 525.20, 39.70, 'Healthy', 'Weekly monitoring - optimal health. Weight: 525.2kg, Temperature: 39.7°C'),
(69, 4, '2025-05-16', 556.10, 38.50, 'Healthy', 'Weekly monitoring - optimal health. Weight: 556.1kg, Temperature: 38.5°C'),
(70, 4, '2025-06-22', 416.40, 37.40, 'Healthy', 'Animal appears alert and active. Weight: 416.4kg, Temperature: 37.4°C'),
(71, 4, '2025-06-02', 501.90, 40.20, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 501.9kg, Temperature: 40.2°C'),
(72, 4, '2025-04-25', 494.80, 39.70, 'Sick', 'Infection suspected - isolation recommended. Weight: 494.8kg, Temperature: 39.7°C'),
(73, 4, '2025-05-01', 416.80, 40.00, 'Sick', 'Digestive issues - dietary adjustment needed. Weight: 416.8kg, Temperature: 40°C'),
(74, 4, '2025-05-21', 566.90, 38.20, 'Healthy', 'Weekly monitoring - optimal health. Weight: 566.9kg, Temperature: 38.2°C'),
(75, 4, '2025-04-14', 591.10, 38.60, 'Healthy', 'Weekly monitoring - optimal health. Weight: 591.1kg, Temperature: 38.6°C'),
(76, 5, '2025-07-05', 579.00, 37.20, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 579kg, Temperature: 37.2°C'),
(77, 5, '2025-06-26', 442.50, 40.00, 'Healthy', 'Weekly monitoring - optimal health. Weight: 442.5kg, Temperature: 40°C'),
(78, 5, '2025-06-10', 573.60, 39.60, 'Good', 'Good appetite, normal behavior observed. Weight: 573.6kg, Temperature: 39.6°C'),
(79, 5, '2025-05-02', 538.00, 37.40, 'Good', 'Minor weight fluctuation noted. Weight: 538kg, Temperature: 37.4°C'),
(80, 5, '2025-05-01', 558.10, 38.20, 'Good', 'Good appetite, normal behavior observed. Weight: 558.1kg, Temperature: 38.2°C'),
(81, 5, '2025-07-10', 451.80, 39.40, 'Healthy', 'Animal appears alert and active. Weight: 451.8kg, Temperature: 39.4°C'),
(82, 5, '2025-06-16', 560.20, 39.30, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 560.2kg, Temperature: 39.3°C'),
(83, 5, '2025-04-22', 490.10, 39.00, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 490.1kg, Temperature: 39°C'),
(84, 6, '2025-05-25', 73.50, 37.50, 'Good', 'Good appetite, normal behavior observed. Weight: 73.5kg, Temperature: 37.5°C'),
(85, 6, '2025-07-06', 94.20, 39.30, 'Monitoring', 'Close observation recommended. Weight: 94.2kg, Temperature: 39.3°C'),
(86, 6, '2025-04-28', 70.70, 38.10, 'Good', 'Minor weight fluctuation noted. Weight: 70.7kg, Temperature: 38.1°C'),
(87, 6, '2025-05-20', 97.20, 40.20, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 97.2kg, Temperature: 40.2°C'),
(88, 6, '2025-06-18', 62.50, 39.90, 'Healthy', 'Animal appears alert and active. Weight: 62.5kg, Temperature: 39.9°C'),
(89, 6, '2025-05-27', 85.70, 39.30, 'Good', 'Routine checkup - generally healthy. Weight: 85.7kg, Temperature: 39.3°C'),
(90, 6, '2025-05-31', 78.40, 37.50, 'Healthy', 'Animal appears alert and active. Weight: 78.4kg, Temperature: 37.5°C'),
(91, 7, '2025-06-21', 97.80, 37.80, 'Healthy', 'Weekly monitoring - optimal health. Weight: 97.8kg, Temperature: 37.8°C'),
(92, 7, '2025-06-02', 78.40, 40.30, 'Healthy', 'Animal appears alert and active. Weight: 78.4kg, Temperature: 40.3°C'),
(93, 7, '2025-05-15', 91.80, 40.30, 'Good', 'Routine checkup - generally healthy. Weight: 91.8kg, Temperature: 40.3°C'),
(94, 7, '2025-06-02', 86.70, 38.90, 'Monitoring', 'Close observation recommended. Weight: 86.7kg, Temperature: 38.9°C'),
(95, 7, '2025-06-26', 71.00, 40.40, 'Healthy', 'Animal appears alert and active. Weight: 71kg, Temperature: 40.4°C'),
(96, 7, '2025-07-07', 62.50, 40.00, 'Healthy', 'Animal appears alert and active. Weight: 62.5kg, Temperature: 40°C'),
(97, 7, '2025-06-02', 67.80, 40.10, 'Good', 'Routine checkup - generally healthy. Weight: 67.8kg, Temperature: 40.1°C'),
(98, 8, '2025-06-03', 83.80, 37.80, 'Healthy', 'Weekly monitoring - optimal health. Weight: 83.8kg, Temperature: 37.8°C'),
(99, 8, '2025-05-07', 86.90, 39.00, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 86.9kg, Temperature: 39°C'),
(100, 8, '2025-06-01', 82.50, 38.50, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 82.5kg, Temperature: 38.5°C'),
(101, 8, '2025-06-16', 87.80, 39.50, 'Good', 'Routine checkup - generally healthy. Weight: 87.8kg, Temperature: 39.5°C'),
(102, 8, '2025-06-01', 97.70, 38.20, 'Healthy', 'Weekly monitoring - optimal health. Weight: 97.7kg, Temperature: 38.2°C'),
(103, 9, '2025-06-06', 135.90, 38.80, 'Healthy', 'Animal appears alert and active. Weight: 135.9kg, Temperature: 38.8°C'),
(104, 9, '2025-06-30', 80.20, 39.60, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 80.2kg, Temperature: 39.6°C'),
(105, 9, '2025-06-18', 172.70, 38.20, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 172.7kg, Temperature: 38.2°C'),
(106, 10, '2025-05-17', 192.50, 40.70, 'Healthy', 'Animal appears alert and active. Weight: 192.5kg, Temperature: 40.7°C'),
(107, 10, '2025-07-11', 180.30, 39.60, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 180.3kg, Temperature: 39.6°C'),
(108, 10, '2025-05-28', 123.10, 39.30, 'Good', 'Good appetite, normal behavior observed. Weight: 123.1kg, Temperature: 39.3°C'),
(109, 10, '2025-06-22', 129.70, 38.60, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 129.7kg, Temperature: 38.6°C'),
(110, 10, '2025-07-11', 154.70, 38.40, 'Healthy', 'Animal appears alert and active. Weight: 154.7kg, Temperature: 38.4°C'),
(111, 10, '2025-07-08', 180.50, 39.80, 'Critical', 'Life-threatening situation - urgent treatment. Weight: 180.5kg, Temperature: 39.8°C'),
(112, 10, '2025-04-24', 194.20, 40.70, 'Healthy', 'Regular health checkup - all vitals normal. Weight: 194.2kg, Temperature: 40.7°C'),
(113, 10, '2025-05-10', 140.40, 39.80, 'Sick', 'Respiratory symptoms - antibiotic prescribed. Weight: 140.4kg, Temperature: 39.8°C'),
(114, 11, '2025-06-29', 82.00, 39.60, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 82kg, Temperature: 39.6°C'),
(115, 11, '2025-06-12', 74.20, 37.70, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 74.2kg, Temperature: 37.7°C'),
(116, 11, '2025-04-23', 87.60, 38.30, 'Healthy', 'Excellent condition, no concerns. Weight: 87.6kg, Temperature: 38.3°C'),
(117, 12, '2025-04-18', 593.60, 37.70, 'Healthy', 'Weekly monitoring - optimal health. Weight: 593.6kg, Temperature: 37.7°C'),
(118, 12, '2025-05-15', 546.80, 38.70, 'Healthy', 'Excellent condition, no concerns. Weight: 546.8kg, Temperature: 38.7°C'),
(119, 12, '2025-06-28', 571.90, 38.80, 'Monitoring', 'Preventive monitoring initiated. Weight: 571.9kg, Temperature: 38.8°C'),
(120, 12, '2025-06-27', 461.20, 39.10, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 461.2kg, Temperature: 39.1°C'),
(121, 12, '2025-05-25', 559.60, 37.80, 'Healthy', 'Comprehensive examination - all systems normal. Weight: 559.6kg, Temperature: 37.8°C'),
(122, 12, '2025-05-17', 435.40, 38.40, 'Healthy', 'Animal appears alert and active. Weight: 435.4kg, Temperature: 38.4°C'),
(123, 12, '2025-06-15', 488.30, 38.20, 'Good', 'Good appetite, normal behavior observed. Weight: 488.3kg, Temperature: 38.2°C'),
(124, 13, '2025-04-15', 443.00, 39.00, 'Healthy', 'Excellent condition, no concerns. Weight: 443kg, Temperature: 39°C'),
(125, 13, '2025-06-06', 505.00, 40.00, 'Healthy', 'Excellent condition, no concerns. Weight: 505kg, Temperature: 40°C'),
(126, 13, '2025-06-19', 459.90, 40.10, 'Healthy', 'Animal appears alert and active. Weight: 459.9kg, Temperature: 40.1°C'),
(127, 13, '2025-06-19', 574.50, 40.20, 'Healthy', 'Animal appears alert and active. Weight: 574.5kg, Temperature: 40.2°C'),
(128, 13, '2025-05-12', 524.50, 40.80, 'Healthy', 'Weekly monitoring - optimal health. Weight: 524.5kg, Temperature: 40.8°C');

-- --------------------------------------------------------

--
-- Table structure for table `sensor_data`
--

CREATE TABLE `sensor_data` (
  `id` int(11) NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `temperature` float DEFAULT NULL,
  `humidity` float DEFAULT NULL,
  `activity_level` float DEFAULT NULL,
  `ir_status` tinyint(1) DEFAULT NULL,
  `location` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sensor_data`
--

INSERT INTO `sensor_data` (`id`, `timestamp`, `temperature`, `humidity`, `activity_level`, `ir_status`, `location`) VALUES
(1, '2024-07-12 23:00:00', 22.5, 65, 15.2, 0, 'Barn A'),
(2, '2024-07-12 22:00:00', 23.1, 63.5, 18.7, 1, 'Barn A'),
(3, '2024-07-12 21:00:00', 24.2, 62, 25.3, 1, 'Barn A'),
(4, '2024-07-12 20:00:00', 25.8, 60.5, 32.1, 1, 'Barn A'),
(5, '2024-07-12 19:00:00', 26.5, 58, 45.8, 1, 'Barn A'),
(6, '2024-07-12 18:00:00', 27.2, 56.5, 52.3, 1, 'Barn A'),
(7, '2024-07-12 17:00:00', 28, 55, 48.7, 1, 'Barn A'),
(8, '2024-07-12 16:00:00', 28.5, 54.2, 42.1, 1, 'Barn A'),
(9, '2024-07-12 15:00:00', 29.1, 53.8, 38.5, 1, 'Barn A'),
(10, '2024-07-12 14:00:00', 29.8, 52.5, 35.2, 1, 'Barn A'),
(11, '2024-07-12 13:00:00', 30.2, 51, 40.8, 1, 'Barn A'),
(12, '2024-07-12 12:00:00', 30.5, 50.5, 45.3, 1, 'Barn A'),
(13, '2024-07-12 23:00:00', 18.5, 75, 8.2, 0, 'Pasture 1'),
(14, '2024-07-12 22:00:00', 19.2, 73.5, 12.1, 0, 'Pasture 1'),
(15, '2024-07-12 21:00:00', 20.8, 71, 15.7, 1, 'Pasture 1'),
(16, '2024-07-12 20:00:00', 22.5, 68.5, 22.3, 1, 'Pasture 1'),
(17, '2024-07-12 19:00:00', 24.1, 65, 35.8, 1, 'Pasture 1'),
(18, '2024-07-12 18:00:00', 25.8, 62.5, 42.1, 1, 'Pasture 1'),
(19, '2024-07-12 17:00:00', 27.2, 60, 38.7, 1, 'Pasture 1'),
(20, '2024-07-12 16:00:00', 28.5, 58.5, 35.2, 1, 'Pasture 1'),
(21, '2024-07-12 15:00:00', 29.8, 57, 32.8, 1, 'Pasture 1'),
(22, '2024-07-12 14:00:00', 31.2, 55.5, 30.5, 1, 'Pasture 1'),
(23, '2024-07-12 13:00:00', 32.1, 54, 28.2, 1, 'Pasture 1'),
(24, '2024-07-12 12:00:00', 32.8, 52.5, 25.8, 1, 'Pasture 1'),
(25, '2024-07-12 23:00:00', 21.8, 68, 12.5, 0, 'Barn B'),
(26, '2024-07-12 22:00:00', 22.5, 66.5, 16.8, 1, 'Barn B'),
(27, '2024-07-12 21:00:00', 23.8, 64, 22.1, 1, 'Barn B'),
(28, '2024-07-12 20:00:00', 25.2, 61.5, 28.7, 1, 'Barn B'),
(29, '2024-07-12 19:00:00', 26.8, 59, 38.2, 1, 'Barn B'),
(30, '2024-07-12 18:00:00', 27.5, 57.5, 42.8, 1, 'Barn B'),
(31, '2024-07-12 17:00:00', 28.2, 56, 40.5, 1, 'Barn B'),
(32, '2024-07-12 16:00:00', 28.8, 55.2, 36.1, 1, 'Barn B'),
(33, '2024-07-12 15:00:00', 29.5, 54.8, 33.7, 1, 'Barn B'),
(34, '2024-07-12 14:00:00', 30.1, 53.5, 31.2, 1, 'Barn B'),
(35, '2024-07-12 13:00:00', 30.8, 52, 34.8, 1, 'Barn B'),
(36, '2024-07-12 12:00:00', 31.2, 51.5, 38.5, 1, 'Barn B'),
(37, '2025-07-13 03:07:56', 27, 60, 77, 1, 'Barn A'),
(38, '2025-07-13 03:08:08', 29.9, 68, 75, 1, 'Barn B'),
(39, '2025-07-13 03:08:09', 22.5, 78, 31, 1, 'Barn A'),
(40, '2025-07-13 03:08:10', 29.9, 68, 75, 1, 'Barn B'),
(41, '2025-07-13 03:08:24', 30.2, 68, 70, 1, 'Barn B'),
(42, '2025-07-13 03:08:40', 29.6, 76, 70, 1, 'Barn A'),
(43, '2025-07-13 03:08:41', 25.9, 61, 35, 1, 'Barn B'),
(44, '2025-07-13 03:09:10', 25.4, 59, 33, 1, 'Barn A'),
(45, '2025-07-13 03:09:11', 31.8, 69, 67, 1, 'Barn B'),
(46, '2025-07-13 03:09:40', 29.3, 58, 30, 1, 'Barn A'),
(47, '2025-07-13 03:09:41', 31.9, 58, 21, 1, 'Barn B'),
(48, '2025-07-13 03:10:11', 28.4, 63, 31, 1, 'Barn A'),
(49, '2025-07-13 03:10:11', 24, 72, 40, 1, 'Barn B'),
(50, '2025-07-13 03:10:40', 28.5, 59, 31, 1, 'Barn A'),
(51, '2025-07-13 03:10:47', 22.6, 51, 71, 1, 'Barn B'),
(52, '2025-07-13 03:12:08', 27.4, 50, 75, 1, 'Barn A'),
(53, '2025-07-13 03:12:08', 26.5, 73, 25, 1, 'Barn B'),
(54, '2025-07-13 03:12:09', 23.6, 57, 57, 1, 'Barn A'),
(55, '2025-07-13 03:12:10', 23.6, 71, 68, 1, 'Barn B'),
(56, '2025-07-13 03:12:40', 24.8, 71, 79, 1, 'Barn A'),
(57, '2025-07-13 03:12:41', 30, 63, 45, 1, 'Barn B'),
(58, '2025-07-13 03:13:10', 21.6, 61, 59, 1, 'Barn A'),
(59, '2025-07-13 03:13:11', 22.4, 45, 25, 1, 'Barn B'),
(60, '2025-07-13 03:14:47', 22.8, 54, 42, 1, 'Barn A'),
(61, '2025-07-13 03:14:48', 27.1, 55, 64, 1, 'Barn B'),
(62, '2025-07-13 03:15:47', 32.3, 64, 79, 1, 'Barn A'),
(63, '2025-07-13 03:15:48', 22.1, 64, 41, 1, 'Barn B'),
(64, '2025-07-13 03:16:47', 31.8, 67, 65, 1, 'Barn A'),
(65, '2025-07-13 03:16:48', 33.1, 62, 37, 1, 'Barn B'),
(66, '2025-07-13 03:17:47', 25.7, 51, 62, 1, 'Barn A'),
(67, '2025-07-13 03:17:48', 32.5, 69, 39, 1, 'Barn B'),
(68, '2025-07-13 03:18:47', 20.8, 57, 30, 1, 'Barn A'),
(69, '2025-07-13 03:18:48', 27.7, 65, 37, 1, 'Barn B'),
(70, '2025-07-13 03:19:47', 22.9, 66, 35, 1, 'Barn A'),
(71, '2025-07-13 03:19:48', 24.4, 66, 53, 1, 'Barn B'),
(72, '2025-07-13 03:20:47', 31.6, 59, 53, 1, 'Barn A'),
(73, '2025-07-13 03:20:48', 22.8, 66, 79, 1, 'Barn B'),
(74, '2025-07-13 03:21:47', 22.1, 63, 59, 1, 'Barn A'),
(75, '2025-07-13 03:21:48', 28.9, 63, 56, 1, 'Barn B'),
(76, '2025-07-13 03:22:47', 23.3, 56, 30, 1, 'Barn A'),
(77, '2025-07-13 03:22:48', 22.8, 65, 44, 1, 'Barn B'),
(78, '2025-07-13 03:23:47', 22.7, 53, 72, 1, 'Barn A'),
(79, '2025-07-13 03:23:48', 30.9, 49, 35, 1, 'Barn B'),
(80, '2025-07-13 03:24:47', 24.6, 57, 32, 1, 'Barn A'),
(81, '2025-07-13 03:24:48', 24.9, 66, 31, 1, 'Barn B'),
(82, '2025-07-13 03:25:47', 21.3, 55, 56, 1, 'Barn A'),
(83, '2025-07-13 03:25:48', 24.9, 77, 33, 1, 'Barn B'),
(84, '2025-07-13 03:26:47', 23.7, 62, 77, 1, 'Barn A'),
(85, '2025-07-13 03:26:48', 23.2, 47, 66, 1, 'Barn B'),
(86, '2025-07-13 03:27:47', 24.8, 77, 55, 1, 'Barn A'),
(87, '2025-07-13 03:27:48', 25.1, 60, 61, 1, 'Barn B'),
(88, '2025-07-13 03:28:47', 32.4, 55, 32, 1, 'Barn A'),
(89, '2025-07-13 03:28:48', 23.8, 49, 73, 1, 'Barn B'),
(90, '2025-07-13 03:29:47', 33.6, 61, 62, 1, 'Barn A'),
(91, '2025-07-13 03:29:48', 27.6, 61, 24, 1, 'Barn B'),
(92, '2025-07-13 03:30:47', 34.3, 52, 63, 1, 'Barn A'),
(93, '2025-07-13 03:30:48', 28.8, 53, 49, 1, 'Barn B'),
(94, '2025-07-13 03:31:48', 27.9, 67, 48, 1, 'Barn A'),
(95, '2025-07-13 03:31:48', 26.5, 62, 38, 1, 'Barn B'),
(96, '2025-07-13 03:32:47', 21.5, 65, 75, 1, 'Barn A'),
(97, '2025-07-13 03:32:48', 30.4, 46, 47, 1, 'Barn B'),
(98, '2025-07-13 03:33:47', 27.9, 66, 62, 1, 'Barn A'),
(99, '2025-07-13 03:33:48', 29.6, 51, 32, 1, 'Barn B'),
(100, '2025-07-13 03:34:47', 20.6, 70, 55, 1, 'Barn A'),
(101, '2025-07-13 03:34:48', 27.1, 59, 42, 1, 'Barn B'),
(102, '2025-07-13 03:35:47', 24.7, 65, 64, 1, 'Barn A'),
(103, '2025-07-13 03:35:48', 33, 53, 40, 1, 'Barn B'),
(104, '2025-07-13 03:36:48', 32.8, 59, 53, 1, 'Barn A'),
(105, '2025-07-13 03:36:48', 30, 54, 32, 1, 'Barn B'),
(106, '2025-07-13 03:37:48', 22, 64, 50, 1, 'Barn A'),
(107, '2025-07-13 03:37:48', 29.1, 50, 35, 1, 'Barn B'),
(108, '2025-07-13 03:38:47', 25.3, 77, 32, 1, 'Barn A'),
(109, '2025-07-13 03:38:48', 33.6, 53, 51, 1, 'Barn B'),
(110, '2025-07-13 03:39:48', 23.5, 58, 70, 1, 'Barn A'),
(111, '2025-07-13 03:39:48', 22.7, 64, 28, 1, 'Barn B'),
(112, '2025-07-13 03:40:48', 26, 68, 52, 1, 'Barn A'),
(113, '2025-07-13 03:40:48', 28.3, 75, 50, 1, 'Barn B'),
(114, '2025-07-13 03:41:48', 31, 72, 30, 1, 'Barn A'),
(115, '2025-07-13 03:41:48', 32.2, 66, 65, 1, 'Barn B'),
(116, '2025-07-13 03:42:47', 32.6, 63, 59, 1, 'Barn A'),
(117, '2025-07-13 03:42:48', 29.4, 68, 64, 1, 'Barn B'),
(118, '2025-07-13 03:43:47', 31.7, 60, 79, 1, 'Barn A'),
(119, '2025-07-13 03:43:48', 31.6, 73, 21, 1, 'Barn B'),
(120, '2025-07-13 03:44:48', 23.2, 64, 45, 1, 'Barn A'),
(121, '2025-07-13 03:44:48', 26.6, 46, 47, 1, 'Barn B'),
(122, '2025-07-13 03:45:48', 30.9, 62, 76, 1, 'Barn A'),
(123, '2025-07-13 03:45:48', 22.6, 45, 77, 1, 'Barn B'),
(124, '2025-07-13 03:46:47', 29.2, 51, 57, 1, 'Barn A'),
(125, '2025-07-13 03:46:48', 31.2, 55, 24, 1, 'Barn B'),
(126, '2025-07-13 03:47:48', 26.4, 53, 42, 1, 'Barn A'),
(127, '2025-07-13 03:47:48', 30.5, 59, 78, 1, 'Barn B'),
(128, '2025-07-13 03:48:47', 31.9, 70, 48, 1, 'Barn A'),
(129, '2025-07-13 03:48:48', 32.3, 60, 73, 1, 'Barn B'),
(130, '2025-07-13 03:49:48', 28.5, 70, 55, 1, 'Barn A'),
(131, '2025-07-13 03:49:48', 30.8, 57, 47, 1, 'Barn B'),
(132, '2025-07-13 03:50:47', 20.7, 67, 58, 1, 'Barn A'),
(133, '2025-07-13 03:50:48', 33.6, 78, 58, 1, 'Barn B'),
(134, '2025-07-13 03:51:48', 22.4, 68, 64, 1, 'Barn A'),
(135, '2025-07-13 03:51:48', 31.1, 59, 60, 1, 'Barn B'),
(136, '2025-07-13 03:52:48', 29.4, 64, 42, 1, 'Barn A'),
(137, '2025-07-13 03:52:48', 31.3, 61, 75, 1, 'Barn B'),
(138, '2025-07-13 03:53:47', 25.4, 62, 67, 1, 'Barn A'),
(139, '2025-07-13 03:53:48', 25.5, 78, 78, 1, 'Barn B'),
(140, '2025-07-13 03:54:47', 24, 78, 61, 1, 'Barn A'),
(141, '2025-07-13 03:54:48', 32.6, 55, 40, 1, 'Barn B'),
(142, '2025-07-13 03:55:48', 21.1, 60, 66, 1, 'Barn A'),
(143, '2025-07-13 03:55:48', 23.8, 46, 49, 1, 'Barn B'),
(144, '2025-07-13 03:56:47', 27.2, 55, 64, 1, 'Barn A'),
(145, '2025-07-13 03:56:48', 23.5, 55, 30, 1, 'Barn B'),
(146, '2025-07-13 03:57:48', 22.7, 57, 43, 1, 'Barn A'),
(147, '2025-07-13 03:57:48', 25.6, 74, 77, 1, 'Barn B'),
(148, '2025-07-13 03:58:48', 21.6, 79, 53, 1, 'Barn A'),
(149, '2025-07-13 03:58:48', 30.7, 49, 31, 1, 'Barn B'),
(150, '2025-07-13 03:59:48', 28.8, 76, 33, 1, 'Barn A'),
(151, '2025-07-13 03:59:48', 27.3, 54, 63, 1, 'Barn B'),
(152, '2025-07-13 04:00:47', 21.5, 56, 55, 1, 'Barn A'),
(153, '2025-07-13 04:00:48', 24.4, 48, 56, 1, 'Barn B'),
(154, '2025-07-13 04:01:47', 32.3, 77, 66, 1, 'Barn A'),
(155, '2025-07-13 04:01:48', 32.8, 77, 41, 1, 'Barn B'),
(156, '2025-07-13 04:02:47', 29.5, 54, 78, 1, 'Barn A'),
(157, '2025-07-13 04:02:48', 31.8, 77, 39, 1, 'Barn B'),
(158, '2025-07-13 04:03:47', 26.9, 76, 46, 1, 'Barn A'),
(159, '2025-07-13 04:03:48', 31.3, 64, 56, 1, 'Barn B'),
(160, '2025-07-13 04:04:48', 34.9, 60, 31, 1, 'Barn A'),
(161, '2025-07-13 04:04:48', 26.1, 53, 62, 1, 'Barn B'),
(162, '2025-07-13 04:05:48', 30.1, 51, 58, 1, 'Barn A'),
(163, '2025-07-13 04:05:48', 27.3, 60, 54, 1, 'Barn B'),
(164, '2025-07-13 04:06:47', 26.5, 64, 35, 1, 'Barn A'),
(165, '2025-07-13 04:06:48', 30.7, 49, 49, 1, 'Barn B'),
(166, '2025-07-13 04:06:50', 29.2, 60, 50, 1, 'Barn A'),
(167, '2025-07-13 04:06:50', 23.6, 49, 57, 1, 'Barn B');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `alerts`
--
ALTER TABLE `alerts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `animals`
--
ALTER TABLE `animals`
  ADD PRIMARY KEY (`animal_id`),
  ADD UNIQUE KEY `tag_number` (`tag_number`),
  ADD KEY `idx_animals_photo` (`photo`);

--
-- Indexes for table `feeding_records`
--
ALTER TABLE `feeding_records`
  ADD PRIMARY KEY (`record_id`),
  ADD KEY `animal_id` (`animal_id`);

--
-- Indexes for table `health_records`
--
ALTER TABLE `health_records`
  ADD PRIMARY KEY (`record_id`),
  ADD KEY `animal_id` (`animal_id`);

--
-- Indexes for table `sensor_data`
--
ALTER TABLE `sensor_data`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `alerts`
--
ALTER TABLE `alerts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `animals`
--
ALTER TABLE `animals`
  MODIFY `animal_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `feeding_records`
--
ALTER TABLE `feeding_records`
  MODIFY `record_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `health_records`
--
ALTER TABLE `health_records`
  MODIFY `record_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=129;

--
-- AUTO_INCREMENT for table `sensor_data`
--
ALTER TABLE `sensor_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=168;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `feeding_records`
--
ALTER TABLE `feeding_records`
  ADD CONSTRAINT `feeding_records_ibfk_1` FOREIGN KEY (`animal_id`) REFERENCES `animals` (`animal_id`);

--
-- Constraints for table `health_records`
--
ALTER TABLE `health_records`
  ADD CONSTRAINT `health_records_ibfk_1` FOREIGN KEY (`animal_id`) REFERENCES `animals` (`animal_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
