<?php
/**
 * Navigation Test Page
 * Simple page to test all navigation links
 */
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Livestock Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .navbar {
            background: #4a6cf7;
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 1rem 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-radius: 0;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .test-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            text-align: center;
        }

        .test-card h3 {
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .test-link {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #4a6cf7;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background-color 0.3s;
            margin: 0.5rem;
        }

        .test-link:hover {
            background: #3c5aa6;
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
        }

        .status.success {
            background: #c6f6d5;
            color: #22543d;
        }

        .status.error {
            background: #fed7d7;
            color: #742a2a;
        }

        .file-list {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-top: 2rem;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .exists {
            background: #c6f6d5;
            color: #22543d;
        }

        .missing {
            background: #fed7d7;
            color: #742a2a;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">Livestock Manager</div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="esp32_dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="animals.php" class="nav-link">
                        <i class="fas fa-paw"></i> Animals
                    </a>
                </li>
                <li class="nav-item">
                    <a href="health.php" class="nav-link">
                        <i class="fas fa-heart"></i> Health
                    </a>
                </li>
                <li class="nav-item">
                    <a href="iot_data.php" class="nav-link">
                        <i class="fas fa-wifi"></i> IoT Data
                    </a>
                </li>
                <li class="nav-item">
                    <a href="alerts.php" class="nav-link">
                        <i class="fas fa-exclamation-triangle"></i> Alerts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="ai_insights.php" class="nav-link">
                        <i class="fas fa-chart-line"></i> AI Insights
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <h1>🧪 Navigation Test Page</h1>
        <p>This page helps you test all navigation links and verify that all files exist.</p>

        <!-- File Status Check -->
        <div class="file-list">
            <h3>📁 File Status Check</h3>
            <?php
            $files = [
                'esp32_dashboard.php' => 'Dashboard',
                'animals.php' => 'Animals',
                'health.php' => 'Health',
                'iot_data.php' => 'IoT Data',
                'alerts.php' => 'Alerts',
                'ai_insights.php' => 'AI Insights',
                'api/config.php' => 'API Config',
                'api/sensor_data.php' => 'Sensor Data API',
                'api/alerts.php' => 'Alerts API',
                'api/devices.php' => 'Devices API'
            ];

            foreach ($files as $file => $name) {
                $exists = file_exists($file);
                echo '<div class="file-item">';
                echo '<span><strong>' . htmlspecialchars($name) . '</strong> (' . htmlspecialchars($file) . ')</span>';
                echo '<span class="file-status ' . ($exists ? 'exists' : 'missing') . '">';
                echo $exists ? '✅ Exists' : '❌ Missing';
                echo '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- Navigation Test Links -->
        <div class="test-grid">
            <div class="test-card">
                <h3>🏠 Dashboard</h3>
                <p>Main overview with IoT data and metrics</p>
                <a href="esp32_dashboard.php" class="test-link">Test Dashboard</a>
            </div>

            <div class="test-card">
                <h3>🐄 Animals</h3>
                <p>Animal management and records</p>
                <a href="animals.php" class="test-link">Test Animals</a>
            </div>

            <div class="test-card">
                <h3>❤️ Health</h3>
                <p>Health monitoring and medical records</p>
                <a href="health.php" class="test-link">Test Health</a>
            </div>

            <div class="test-card">
                <h3>📡 IoT Data</h3>
                <p>ESP32 sensor data and monitoring</p>
                <a href="iot_data.php" class="test-link">Test IoT Data</a>
            </div>

            <div class="test-card">
                <h3>🚨 Alerts</h3>
                <p>Alert management and notifications</p>
                <a href="alerts.php" class="test-link">Test Alerts</a>
            </div>

            <div class="test-card">
                <h3>🤖 AI Insights</h3>
                <p>Advanced analytics and predictions</p>
                <a href="ai_insights.php" class="test-link">Test AI Insights</a>
            </div>
        </div>

        <!-- Database Connection Test -->
        <div class="file-list">
            <h3>🗄️ Database Connection Test</h3>
            <?php
            try {
                if (file_exists('api/config.php')) {
                    require_once 'api/config.php';
                    $pdo = getDBConnection();
                    
                    // Test basic query
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM animals");
                    $result = $stmt->fetch();
                    
                    echo '<div class="status success">';
                    echo '✅ Database connection successful!<br>';
                    echo 'Found ' . $result['count'] . ' animals in database.';
                    echo '</div>';
                } else {
                    echo '<div class="status error">';
                    echo '❌ API config file not found!';
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="status error">';
                echo '❌ Database connection failed: ' . htmlspecialchars($e->getMessage());
                echo '</div>';
            }
            ?>
        </div>

        <!-- Troubleshooting Tips -->
        <div class="file-list">
            <h3>🔧 Troubleshooting Tips</h3>
            <div style="padding: 1rem; background: #f7fafc; border-radius: 8px;">
                <h4>If navigation links don't work:</h4>
                <ul style="margin-left: 2rem; margin-top: 0.5rem;">
                    <li>Make sure all PHP files are in the same directory as this test file</li>
                    <li>Check that your web server (XAMPP) is running</li>
                    <li>Verify the database connection in api/config.php</li>
                    <li>Check browser console for JavaScript errors</li>
                    <li>Ensure all files have proper PHP opening tags</li>
                </ul>
                
                <h4 style="margin-top: 1rem;">File locations should be:</h4>
                <ul style="margin-left: 2rem; margin-top: 0.5rem;">
                    <li><code>c:\xampp\htdocs\livestock\esp32_dashboard.php</code></li>
                    <li><code>c:\xampp\htdocs\livestock\animals.php</code></li>
                    <li><code>c:\xampp\htdocs\livestock\health.php</code></li>
                    <li><code>c:\xampp\htdocs\livestock\iot_data.php</code></li>
                    <li><code>c:\xampp\htdocs\livestock\alerts.php</code></li>
                    <li><code>c:\xampp\htdocs\livestock\ai_insights.php</code></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
