<?php
/**
 * File Checker - Check for PHP syntax errors
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>File Checker</title>";
echo "<style>body{font-family:Arial;padding:20px;} .error{color:red;} .success{color:green;} .file{margin:10px 0;padding:10px;border:1px solid #ddd;}</style>";
echo "</head><body>";
echo "<h1>🔍 PHP File Syntax Checker</h1>";

$files = [
    'esp32_dashboard.php',
    'animals.php', 
    'health.php',
    'iot_data.php',
    'alerts.php',
    'ai_insights.php'
];

foreach ($files as $file) {
    echo "<div class='file'>";
    echo "<h3>📄 Checking: $file</h3>";
    
    if (!file_exists($file)) {
        echo "<div class='error'>❌ File does not exist!</div>";
        continue;
    }
    
    // Check PHP syntax
    $output = [];
    $return_var = 0;
    exec("php -l \"$file\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<div class='success'>✅ Syntax OK</div>";
        
        // Try to include and check for fatal errors
        ob_start();
        $error = false;
        try {
            // Don't actually include, just check if we can read it
            $content = file_get_contents($file);
            if (strpos($content, '<?php') === false && strpos($content, '<?') === false) {
                echo "<div class='error'>⚠️ Warning: No PHP opening tag found</div>";
            }
            if (strpos($content, 'require_once') !== false) {
                echo "<div class='success'>📦 Has dependencies</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ob_end_clean();
        
    } else {
        echo "<div class='error'>❌ Syntax Error:</div>";
        echo "<pre>" . htmlspecialchars(implode("\n", $output)) . "</pre>";
    }
    
    // Check file size
    $size = filesize($file);
    echo "<div>📊 File size: " . number_format($size) . " bytes</div>";
    
    echo "</div>";
}

// Check API directory
echo "<div class='file'>";
echo "<h3>📁 API Directory Check</h3>";
if (is_dir('api')) {
    echo "<div class='success'>✅ API directory exists</div>";
    $api_files = ['config.php', 'sensor_data.php', 'alerts.php', 'devices.php'];
    foreach ($api_files as $api_file) {
        $full_path = "api/$api_file";
        if (file_exists($full_path)) {
            echo "<div class='success'>✅ $api_file exists</div>";
        } else {
            echo "<div class='error'>❌ $api_file missing</div>";
        }
    }
} else {
    echo "<div class='error'>❌ API directory does not exist</div>";
}
echo "</div>";

// Test database connection
echo "<div class='file'>";
echo "<h3>🗄️ Database Connection Test</h3>";
if (file_exists('api/config.php')) {
    try {
        require_once 'api/config.php';
        $pdo = getDBConnection();
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test some basic queries
        $tables = ['animals', 'health_records', 'esp32_devices'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "<div class='success'>✅ Table '$table': {$result['count']} records</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Table '$table': " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
} else {
    echo "<div class='error'>❌ API config file not found</div>";
}
echo "</div>";

echo "<h2>🔗 Quick Navigation Test</h2>";
echo "<p>Click these links to test navigation:</p>";
$nav_links = [
    'esp32_dashboard.php' => 'Dashboard',
    'animals.php' => 'Animals', 
    'health.php' => 'Health',
    'iot_data.php' => 'IoT Data',
    'alerts.php' => 'Alerts',
    'ai_insights.php' => 'AI Insights'
];

foreach ($nav_links as $file => $name) {
    if (file_exists($file)) {
        echo "<a href='$file' style='display:inline-block;margin:5px;padding:10px;background:#4a6cf7;color:white;text-decoration:none;border-radius:5px;'>$name</a> ";
    } else {
        echo "<span style='display:inline-block;margin:5px;padding:10px;background:#ccc;color:#666;border-radius:5px;'>$name (Missing)</span> ";
    }
}

echo "</body></html>";
?>
