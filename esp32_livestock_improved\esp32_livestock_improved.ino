
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <Wire.h>
#include <MPU9250.h>

// WiFi credentials
const char* ssid = "Youngboy Never Broke Again";
const char* password = "NeverBroke!";

// Server configuration
const char* serverURL = "http://192.168.0.180/livestock/api/sensor_data.php";

// Device configuration
const String deviceId = "ESP32_001";
const String location = "Barn A";

// Pin definitions (using your preferred pins)
#define DHT_PIN 2        // GPIO2 (D2)
#define DHT_TYPE DHT11
#define BUZZER_PIN 4     // GPIO4 (D4)
#define LED_PIN 5        // GPIO5 (D5) - Status LED
#define SDA_PIN 18       // GPIO18 for I2C SDA
#define SCL_PIN 19       // GPIO19 for I2C SCL

// Sensor objects
DHT dht(DHT_PIN, DHT_TYPE);
MPU9250 mpu;

// Timing variables
unsigned long lastSensorRead = 0;
unsigned long lastDataSend = 0;
const unsigned long SENSOR_INTERVAL = 5000;  // Read sensors every 5 seconds
const unsigned long SEND_INTERVAL = 30000;   // Send data every 30 seconds

// Sensor data structure
struct SensorData {
  float temperature = -999;
  float humidity = -999;
  float activityLevel = 0;
  bool motionDetected = false;
  bool tempValid = false;
  bool humidityValid = false;
  bool mpuValid = false;
};

SensorData currentData;

void setup() {
  Serial.begin(115200);
  Serial.println("=== ESP32 Livestock Monitor Starting ===");
  
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BUZZER_PIN, OUTPUT);
  
  // Status LED - starting up
  digitalWrite(LED_PIN, HIGH);
  
  // Initialize I2C with custom pins
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz for better stability
  
  // Initialize DHT sensor
  dht.begin();
  Serial.println("DHT11 sensor initialized");
  
  // Initialize MPU9250
  if (mpu.setup(0x68)) { // Try primary I2C address
    Serial.println("MPU9250 initialized successfully at 0x68");
  } else if (mpu.setup(0x69)) { // Try alternate I2C address
    Serial.println("MPU9250 initialized successfully at 0x69");
  } else {
    Serial.println("MPU9250 initialization failed - check wiring");
  }
  
  // Connect to WiFi
  connectToWiFi();
  
  // Status LED - ready
  digitalWrite(LED_PIN, LOW);
  
  Serial.println("=== Setup Complete ===");
}

void loop() {
  unsigned long currentTime = millis();
  
  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi disconnected, reconnecting...");
    connectToWiFi();
  }
  
  // Read sensors periodically
  if (currentTime - lastSensorRead >= SENSOR_INTERVAL) {
    readSensors();
    lastSensorRead = currentTime;
  }
  
  // Send data periodically
  if (currentTime - lastDataSend >= SEND_INTERVAL) {
    sendSensorData();
    lastDataSend = currentTime;
  }
  
  // Small delay to prevent watchdog issues
  delay(100);
}

void connectToWiFi() {
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("WiFi connected successfully!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
    
    // Success beep
    digitalWrite(BUZZER_PIN, HIGH);
    delay(100);
    digitalWrite(BUZZER_PIN, LOW);
  } else {
    Serial.println();
    Serial.println("WiFi connection failed!");
    
    // Error beeps
    for (int i = 0; i < 3; i++) {
      digitalWrite(BUZZER_PIN, HIGH);
      delay(200);
      digitalWrite(BUZZER_PIN, LOW);
      delay(200);
    }
  }
}

void readSensors() {
  Serial.println("=== Reading Sensors ===");
  
  // Status LED on during reading
  digitalWrite(LED_PIN, HIGH);
  
  // Read DHT11 sensor with multiple attempts
  readDHTSensor();
  
  // Small delay between sensor reads
  delay(100);
  
  // Read MPU9250 sensor
  readMPUSensor();
  
  // Print current readings
  printSensorData();
  
  // Status LED off
  digitalWrite(LED_PIN, LOW);
}

void readDHTSensor() {
  // Try reading DHT sensor multiple times
  for (int attempt = 0; attempt < 3; attempt++) {
    float temp = dht.readTemperature();
    float hum = dht.readHumidity();
    
    // Check if readings are valid
    if (!isnan(temp) && !isnan(hum)) {
      // Additional validation for reasonable values
      if (temp >= -40 && temp <= 80 && hum >= 0 && hum <= 100) {
        currentData.temperature = temp;
        currentData.humidity = hum;
        currentData.tempValid = true;
        currentData.humidityValid = true;
        Serial.println("DHT11 reading successful");
        return;
      } else {
        Serial.printf("DHT11 values out of range: T=%.1f, H=%.1f\n", temp, hum);
      }
    } else {
      Serial.printf("DHT11 attempt %d failed (NaN values)\n", attempt + 1);
    }
    
    // Wait before retry
    if (attempt < 2) {
      delay(2000); // DHT11 needs time between readings
    }
  }
  
  // All attempts failed
  currentData.temperature = -999;
  currentData.humidity = -999;
  currentData.tempValid = false;
  currentData.humidityValid = false;
  Serial.println("Failed to read from DHT sensor after 3 attempts!");
}

void readMPUSensor() {
  if (mpu.update()) {
    // Calculate activity level from accelerometer
    float ax = mpu.getAccX();
    float ay = mpu.getAccY();
    float az = mpu.getAccZ();
    
    // Calculate magnitude of acceleration (subtract gravity)
    float magnitude = sqrt(ax*ax + ay*ay + az*az) - 1.0; // Subtract 1g for gravity
    currentData.activityLevel = abs(magnitude);
    
    // Detect motion (threshold-based)
    currentData.motionDetected = (currentData.activityLevel > 0.1);
    currentData.mpuValid = true;
    
    Serial.printf("MPU9250 reading successful: Activity=%.3fg\n", currentData.activityLevel);
  } else {
    currentData.activityLevel = 0;
    currentData.motionDetected = false;
    currentData.mpuValid = false;
    Serial.println("Failed to read from MPU9250!");
  }
}

void printSensorData() {
  Serial.println("=== Current Sensor Readings ===");
  Serial.printf("Temperature: %.2f°C %s\n", 
    currentData.temperature, 
    currentData.tempValid ? "(Valid)" : "(Invalid)");
  Serial.printf("Humidity: %.2f%% %s\n", 
    currentData.humidity, 
    currentData.humidityValid ? "(Valid)" : "(Invalid)");
  Serial.printf("Activity Level: %.3fg %s\n", 
    currentData.activityLevel, 
    currentData.mpuValid ? "(Valid)" : "(Invalid)");
  Serial.printf("Motion Detected: %s\n", 
    currentData.motionDetected ? "Yes" : "No");
  Serial.println("================================");
}

void sendSensorData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot send data - WiFi not connected");
    return;
  }
  
  // Create JSON payload
  StaticJsonDocument<300> doc;
  doc["device_id"] = deviceId;
  doc["location"] = location;
  doc["temperature"] = currentData.temperature;
  doc["humidity"] = currentData.humidity;
  doc["activity_level"] = currentData.activityLevel;
  doc["motion_detected"] = currentData.motionDetected ? 1 : 0;
  doc["timestamp"] = millis() / 1000; // Use system uptime in seconds
  
  // Add sensor status for debugging
  doc["temp_valid"] = currentData.tempValid;
  doc["humidity_valid"] = currentData.humidityValid;
  doc["mpu_valid"] = currentData.mpuValid;
  
  String jsonString;
  serializeJson(doc, jsonString);
  
  Serial.println("Sending data: " + jsonString);
  
  // Send HTTP POST request
  HTTPClient http;
  http.begin(serverURL);
  http.addHeader("Content-Type", "application/json");
  
  int httpResponseCode = http.POST(jsonString);
  
  if (httpResponseCode > 0) {
    String response = http.getString();
    Serial.println("HTTP Response: " + String(httpResponseCode));
    Serial.println("Response: " + response);
    
    // Success beep for valid data
    if (currentData.tempValid && currentData.humidityValid) {
      digitalWrite(BUZZER_PIN, HIGH);
      delay(50);
      digitalWrite(BUZZER_PIN, LOW);
    }
  } else {
    Serial.println("HTTP Request failed: " + String(httpResponseCode));
    
    // Error beeps
    for (int i = 0; i < 2; i++) {
      digitalWrite(BUZZER_PIN, HIGH);
      delay(100);
      digitalWrite(BUZZER_PIN, LOW);
      delay(100);
    }
  }
  
  http.end();
}
