<?php
/**
 * Verification Script - Check Weight Removal
 * Verifies that all weight references have been removed from the system
 */

require_once 'api/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Weight Removal Verification</title>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    h1 { color: #333; }
    table { width: 100%; border-collapse: collapse; background: white; margin: 10px 0; }
    th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
    th { background: #f8f9fa; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
</style>";
echo "</head><body>";

echo "<h1>🔍 Weight Removal Verification</h1>";

try {
    $pdo = getDBConnection();
    
    // Check 1: Verify weight column is removed from health_records table
    echo "<div class='result info'>";
    echo "<h3>📋 Database Schema Check</h3>";
    
    $columns_stmt = $pdo->query("DESCRIBE health_records");
    $columns = $columns_stmt->fetchAll();
    
    $weight_column_exists = false;
    echo "<table>";
    echo "<tr><th>Column Name</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        if ($column['Field'] === 'weight') {
            $weight_column_exists = true;
        }
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($weight_column_exists) {
        echo "<p style='color: #dc3545;'><strong>❌ ISSUE:</strong> Weight column still exists in health_records table!</p>";
        echo "<p>Please run the SQL script: <code>remove_weight_from_system.sql</code></p>";
    } else {
        echo "<p style='color: #28a745;'><strong>✅ SUCCESS:</strong> Weight column has been removed from health_records table.</p>";
    }
    echo "</div>";
    
    // Check 2: Test health records functionality
    echo "<div class='result info'>";
    echo "<h3>🏥 Health Records Test</h3>";
    
    $health_test_stmt = $pdo->query("
        SELECT 
            hr.record_id,
            hr.animal_id,
            hr.record_date,
            hr.temperature,
            hr.health_status,
            a.tag_number
        FROM health_records hr
        JOIN animals a ON hr.animal_id = a.animal_id
        WHERE a.status = 'Active'
        ORDER BY hr.record_date DESC
        LIMIT 5
    ");
    $health_records = $health_test_stmt->fetchAll();
    
    if (!empty($health_records)) {
        echo "<p style='color: #28a745;'><strong>✅ SUCCESS:</strong> Health records query works without weight column.</p>";
        echo "<table>";
        echo "<tr><th>Record ID</th><th>Animal</th><th>Date</th><th>Temperature</th><th>Status</th></tr>";
        foreach ($health_records as $record) {
            echo "<tr>";
            echo "<td>" . $record['record_id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['tag_number']) . "</td>";
            echo "<td>" . date('M j, Y', strtotime($record['record_date'])) . "</td>";
            echo "<td>" . ($record['temperature'] ? number_format($record['temperature'], 1) . '°C' : '--') . "</td>";
            echo "<td>" . htmlspecialchars($record['health_status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: #ffc107;'><strong>⚠️ WARNING:</strong> No health records found or query failed.</p>";
    }
    echo "</div>";
    
    // Check 3: Test health statistics without weight
    echo "<div class='result info'>";
    echo "<h3>📊 Health Statistics Test</h3>";
    
    $stats_stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN health_status = 'Healthy' THEN 1 END) as healthy_count,
            COUNT(CASE WHEN health_status IN ('Sick', 'Critical') THEN 1 END) as sick_count,
            AVG(temperature) as avg_temperature
        FROM health_records hr
        JOIN animals a ON hr.animal_id = a.animal_id
        WHERE a.status = 'Active'
        AND hr.record_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stats = $stats_stmt->fetch();
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Records (30 days)</td><td>" . $stats['total_records'] . "</td></tr>";
    echo "<tr><td>Healthy Animals</td><td>" . $stats['healthy_count'] . "</td></tr>";
    echo "<tr><td>Sick Animals</td><td>" . $stats['sick_count'] . "</td></tr>";
    echo "<tr><td>Average Temperature</td><td>" . ($stats['avg_temperature'] ? number_format($stats['avg_temperature'], 1) . '°C' : '--') . "</td></tr>";
    echo "</table>";
    
    echo "<p style='color: #28a745;'><strong>✅ SUCCESS:</strong> Health statistics work without weight calculations.</p>";
    echo "</div>";
    
    // Check 4: File modifications summary
    echo "<div class='result info'>";
    echo "<h3>📁 File Modifications Summary</h3>";
    echo "<ul>";
    echo "<li><strong>✅ manage_health.php:</strong> Removed weight fields from forms and database operations</li>";
    echo "<li><strong>✅ health.php:</strong> Removed weight metrics and table columns</li>";
    echo "<li><strong>✅ esp32_dashboard.php:</strong> Removed weight from health record displays</li>";
    echo "<li><strong>✅ ai_insights.php:</strong> Removed weight trend analysis</li>";
    echo "<li><strong>✅ Database:</strong> Weight column removal script created</li>";
    echo "</ul>";
    echo "</div>";
    
    // Check 5: Pages to test
    echo "<div class='result warning'>";
    echo "<h3>🧪 Manual Testing Required</h3>";
    echo "<p>Please test these pages to ensure they work correctly:</p>";
    echo "<ul>";
    echo "<li><a href='health.php' target='_blank'>Health Dashboard</a> - Check metrics and records display</li>";
    echo "<li><a href='manage_health.php' target='_blank'>Manage Health Records</a> - Test adding/editing records</li>";
    echo "<li><a href='esp32_dashboard.php' target='_blank'>ESP32 Dashboard</a> - Check health records section</li>";
    echo "<li><a href='ai_insights.php' target='_blank'>Analytics & Insights</a> - Verify no weight references</li>";
    echo "</ul>";
    echo "</div>";
    
    // Final status
    if (!$weight_column_exists) {
        echo "<div class='result success'>";
        echo "<h3>🎉 Weight Removal Complete!</h3>";
        echo "<p>All weight (kg) functionality has been successfully removed from your livestock management system.</p>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li>Test the pages listed above</li>";
        echo "<li>Add new health records to verify forms work</li>";
        echo "<li>Check that all displays show correctly without weight</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div class='result error'>";
        echo "<h3>⚠️ Action Required</h3>";
        echo "<p>Please run the database script to complete the weight removal:</p>";
        echo "<ol>";
        echo "<li>Open phpMyAdmin</li>";
        echo "<li>Select your livestock_tracker database</li>";
        echo "<li>Go to SQL tab</li>";
        echo "<li>Copy and paste the contents of <code>remove_weight_from_system.sql</code></li>";
        echo "<li>Click 'Go' to execute</li>";
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Error during verification: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin-top: 2rem;'>";
echo "<a href='esp32_dashboard.php' class='btn'>🏠 Back to Dashboard</a>";
echo "<a href='health.php' class='btn' style='background: #28a745;'>🏥 Health Dashboard</a>";
echo "<a href='manage_health.php' class='btn' style='background: #17a2b8;'>📝 Manage Health</a>";
echo "</div>";

echo "</body></html>";
?>
