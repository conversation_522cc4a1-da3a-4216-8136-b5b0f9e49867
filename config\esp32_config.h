/*
 * ESP32 Configuration Header File
 * Contains all configuration settings for the livestock management system
 * 
 * Instructions:
 * 1. Copy this file to your Arduino libraries folder or include it in your project
 * 2. Update the WiFi credentials and server settings
 * 3. Adjust sensor thresholds as needed for your environment
 */

#ifndef ESP32_CONFIG_H
#define ESP32_CONFIG_H

// ==========================================
// WIFI CONFIGURATION
// ==========================================
// Replace with your network credentials
#define WIFI_SSID "YOUR_WIFI_NETWORK_NAME"
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"

// WiFi connection settings
#define WIFI_TIMEOUT_MS 20000        // WiFi connection timeout in milliseconds
#define WIFI_RETRY_DELAY_MS 500      // Delay between WiFi connection attempts

// ==========================================
// SERVER CONFIGURATION
// ==========================================
// Replace with your server IP address or domain
#define SERVER_HOST "*************"  // Your XAMPP server IP
#define SERVER_PORT 80

// API endpoints
#define SENSOR_DATA_ENDPOINT "http://" SERVER_HOST "/livestock/api/sensor_data.php"
#define ALERTS_ENDPOINT "http://" SERVER_HOST "/livestock/api/alerts.php"
#define DEVICES_ENDPOINT "http://" SERVER_HOST "/livestock/api/devices.php"

// ==========================================
// DEVICE CONFIGURATION
// ==========================================
// Device identification (change for each ESP32)
#define DEVICE_ID "ESP32_001"        // Unique device identifier
#define DEVICE_NAME "Barn A Monitor" // Human-readable device name
#define DEVICE_LOCATION "Barn A"     // Physical location

// ==========================================
// SENSOR PIN CONFIGURATION
// ==========================================
#define DHT_PIN 4                    // D4 (GPIO 4) - DHT11 data pin
#define DHT_TYPE DHT11               // DHT sensor type
#define BUZZER_PIN 5                 // D5 (GPIO 5) - Buzzer signal pin
#define LED_PIN 2                    // Built-in LED pin (same as SDA)
#define SDA_PIN 2                    // D2 (GPIO 2) - I2C SDA pin for MPU9250
#define SCL_PIN 18                   // D18 (GPIO 18) - I2C SCL pin for MPU9250

// ==========================================
// SENSOR THRESHOLDS
// ==========================================
// Temperature thresholds (Celsius)
#define TEMP_HIGH_THRESHOLD 30.0     // High temperature alert
#define TEMP_LOW_THRESHOLD 10.0      // Low temperature alert
#define TEMP_CRITICAL_HIGH 35.0      // Critical high temperature
#define TEMP_CRITICAL_LOW 5.0        // Critical low temperature

// Humidity thresholds (%)
#define HUMIDITY_HIGH_THRESHOLD 80.0 // High humidity alert
#define HUMIDITY_LOW_THRESHOLD 30.0  // Low humidity alert
#define HUMIDITY_CRITICAL_HIGH 90.0  // Critical high humidity
#define HUMIDITY_CRITICAL_LOW 20.0   // Critical low humidity

// Activity thresholds (G-force)
#define ACTIVITY_THRESHOLD 2.0       // Motion detection threshold
#define ACTIVITY_LOW_THRESHOLD 0.5   // Low activity alert
#define ACTIVITY_HIGH_THRESHOLD 10.0 // High activity alert

// ==========================================
// TIMING CONFIGURATION
// ==========================================
#define SENSOR_READ_INTERVAL 5000    // Read sensors every 5 seconds
#define DATA_SEND_INTERVAL 30000     // Send data every 30 seconds
#define ALERT_COOLDOWN 300000        // 5 minutes between same alerts
#define HEARTBEAT_INTERVAL 60000     // Send heartbeat every minute
#define WATCHDOG_TIMEOUT 30000       // Watchdog timeout

// ==========================================
// ALERT CONFIGURATION
// ==========================================
#define BUZZER_ALERT_DURATION 200    // Buzzer beep duration (ms)
#define BUZZER_ALERT_PAUSE 200       // Pause between beeps (ms)
#define BUZZER_ALERT_REPEATS 3       // Number of beeps per alert
#define ALERT_LED_BLINK_RATE 500     // LED blink rate for alerts (ms)

// ==========================================
// COMMUNICATION SETTINGS
// ==========================================
#define HTTP_TIMEOUT 10000           // HTTP request timeout (ms)
#define MAX_RETRY_ATTEMPTS 3         // Maximum retry attempts for failed requests
#define RETRY_DELAY 1000             // Delay between retry attempts (ms)

// ==========================================
// CALIBRATION SETTINGS
// ==========================================
#define MPU_CALIBRATION_SAMPLES 100  // Number of samples for MPU calibration
#define DHT_WARMUP_TIME 2000         // DHT sensor warmup time (ms)
#define SENSOR_STABILIZATION_TIME 5000 // Time to wait for sensors to stabilize

// ==========================================
// DEBUGGING CONFIGURATION
// ==========================================
#define DEBUG_MODE 1                 // Enable/disable debug output
#define SERIAL_BAUD_RATE 115200      // Serial communication baud rate

#if DEBUG_MODE
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
  #define DEBUG_PRINTF(x, ...) Serial.printf(x, __VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(x, ...)
#endif

// ==========================================
// POWER MANAGEMENT
// ==========================================
#define DEEP_SLEEP_ENABLED 0         // Enable deep sleep mode (0=disabled, 1=enabled)
#define DEEP_SLEEP_DURATION 300      // Deep sleep duration in seconds
#define LOW_POWER_MODE_THRESHOLD 3.3 // Battery voltage threshold for low power mode

// ==========================================
// MEMORY MANAGEMENT
// ==========================================
#define MAX_SENSOR_BUFFER_SIZE 10    // Maximum number of sensor readings to buffer
#define JSON_BUFFER_SIZE 1024        // JSON document buffer size

// ==========================================
// ERROR HANDLING
// ==========================================
#define MAX_CONSECUTIVE_ERRORS 5     // Maximum consecutive errors before restart
#define ERROR_LED_BLINK_PATTERN 100  // Error LED blink pattern (ms)

// ==========================================
// SENSOR SPECIFIC SETTINGS
// ==========================================
// DHT11 settings
#define DHT_READ_DELAY 2000          // Minimum delay between DHT readings

// MPU9250 settings
#define MPU_I2C_ADDRESS 0x68         // MPU9250 I2C address
#define MPU_SAMPLE_RATE 100          // MPU sample rate (Hz)
#define MPU_FILTER_BANDWIDTH 42      // Low-pass filter bandwidth

// ==========================================
// VALIDATION MACROS
// ==========================================
#define IS_VALID_TEMPERATURE(t) ((t) > -40.0 && (t) < 80.0)
#define IS_VALID_HUMIDITY(h) ((h) >= 0.0 && (h) <= 100.0)
#define IS_VALID_ACTIVITY(a) ((a) >= 0.0 && (a) < 50.0)

// ==========================================
// DEVICE STATUS CODES
// ==========================================
#define STATUS_OK 0
#define STATUS_WIFI_ERROR 1
#define STATUS_SENSOR_ERROR 2
#define STATUS_SERVER_ERROR 3
#define STATUS_MEMORY_ERROR 4
#define STATUS_UNKNOWN_ERROR 99

#endif // ESP32_CONFIG_H
